<template>
  <el-table-column align="center" v-bind="$attrs">
    <template v-slot="{ row }">
      <component
        v-if="comp"
        :is="comp"
        v-bind="compProps"
        :row="row"
        @action="handleAction"
        @hover="handleHover"
      >
        <template v-for="(_, name) in $slots" #[name]="slotProps">
          <slot :name="name" v-bind="slotProps" />
        </template>
      </component>
    </template>
  </el-table-column>
</template>

<script setup>
import Cautioins from './components/cautioins.vue'
import ProductInfo from './components/productInfo.vue'
import ProductPic from './components/productPic.vue'

const emits = defineEmits(['action', 'hover'])

const props = defineProps({
  compType: {
    type: String,
    required: true,
  },
  compProps: {
    type: Object,
    default: () => ({}),
  },
})

const comp = computed(() => {
  switch (props.compType) {
    case 'cautioins':
      return Cautioins
    case 'productInfo':
      return ProductInfo
    case 'productPic':
      return ProductPic
    default:
      return null
  }
})

function handleAction(...arg) {
  emits('action', ...arg)
}
function handleHover(...arg) {
  emits('hover', ...arg)
}
</script>
