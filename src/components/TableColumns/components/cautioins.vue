<template>
  <div
    class="corner-mark-hint"
    v-if="row.cautionsChange || row.particularEmphasisChange || row.orderSpecificationRequireChange"
  >
    调
  </div>
  <div class="one-ell" v-has-ellipsis:cautionsMore="row" :key="Math.random()">
    <span v-if="row.orderVideoCautionsVO?.cautions && row.orderVideoCautionsVO?.cautions.length">
      模特要求：
    </span>
    <template v-for="(item, i) in row.orderVideoCautionsVO?.cautions" :key="i">
      {{ handleR(item.content) }}
      <br />
    </template>
  </div>
  <div
    class="one-ell"
    v-has-ellipsis:orderSpecificationRequireMore="row"
    :key="Math.random()"
    v-if="row?.orderSpecificationRequire"
  >
    商品规格要求：{{ row?.orderSpecificationRequire || '' }}
  </div>
  <div
    class="one-ell"
    v-has-ellipsis:particularEmphasisMore="row"
    :key="Math.random()"
    v-if="row?.particularEmphasis"
  >
    特别强调：{{ handleR(row?.particularEmphasis) }}
  </div>
  <el-button
    v-btn
    v-if="
      row.cautionsMore ||
      row.orderVideoCautionsVO?.cautionsPics?.length ||
      row.particularEmphasisMore ||
      row.particularEmphasisPic?.length ||
      row.orderSpecificationRequireMore
    "
    link
    type="primary"
    @click="handleAction('匹配模特注意事项', row)"
  >
    更多
  </el-button>
</template>

<script setup>
const emits = defineEmits(['action'])

defineProps({
  row: {
    type: Object,
    required: true,
  },
})

function handleR(data) {
  if (data && data.length > 0) {
    return data.replace(/\r/g, '\r\n') || ''
  } else return ''
}

function handleAction(btn, row) {
  emits('action', btn, row)
}
</script>
