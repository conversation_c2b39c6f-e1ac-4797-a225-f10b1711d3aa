<template>
  <div class="info">
    <div class="product-info">
      <div class="info-head flex-between">
        <div>视频信息</div>
        <el-button type="primary" link @click="openHistoryClip()">
          查看历史剪辑记录>
        </el-button>
      </div>
      <div class="info-content">
        <div v-if="orderVideoUploadLinkSimpleVO">
          <div class="flex-between">
            <div>
              上传信息
              <el-tag style="margin-left: 10px" type="warning" effect="dark">
                {{ uploadStatusV1.find(item => item.value == orderVideoUploadLinkSimpleVO?.status)?.label }}
              </el-tag>
            </div>
            <el-button type="primary" link @click="openHistoryUpload(orderVideoUploadLinkSimpleVO?.id)">
              查看历史上传记录>
            </el-button>
          </div>
          <el-row style="margin-top: 10px" v-if="orderVideoUploadLinkSimpleVO?.needUploadLink">
            <el-col :span="6">
              <div class="label">提交人：</div>
              <div class="content">
                {{ orderVideoUploadLinkSimpleVO.object == 1 ? '商家' : '运营' }}
                {{
                  orderVideoUploadLinkSimpleVO?.object == 1
                    ? orderVideoUploadLinkSimpleVO?.company?.name
                    : orderVideoUploadLinkSimpleVO?.back?.name
                }}
              </div>
            </el-col>
            <el-col :span="6">
              <div class="label">提交时间：</div>
              <div class="content">
                {{ orderVideoUploadLinkSimpleVO?.time }}
              </div>
            </el-col>
            <el-col :span="6">
              <div class="label">上传人：</div>
              <div class="content">
                {{ orderVideoUploadLinkSimpleVO?.uploadUser?.name }}
              </div>
            </el-col>
            <el-col :span="6">
              <div class="label">上传时间：</div>
              <div class="content">
                {{ orderVideoUploadLinkSimpleVO?.uploadTime }}
              </div>
            </el-col>
            <!-- <el-col :span="8">
              <div class="label">视频地址：</div>
              <div
                class="content more-ell link-btn"
                @click="openLink(orderVideoUploadLinkSimpleVO?.uploadLink)"
              >
                {{ orderVideoUploadLinkSimpleVO?.uploadLink }}
              </div>
            </el-col> -->
          </el-row>
          <!-- <el-divider style="margin: 0 0 10px 0"/> -->
          <el-row>
            <el-col :span="6">
              <div class="label">上传链接：</div>
              <div
                class="content more-ell link-btn"
                @click="openLink(orderVideoUploadLinkSimpleVO?.needUploadLink)"
              >
                {{ orderVideoUploadLinkSimpleVO?.needUploadLink }}
              </div>
            </el-col>
            <el-col :span="6">
              <div class="label">上传标题：</div>
              <div class="content">
                {{ orderVideoUploadLinkSimpleVO?.videoTitle }}
              </div>
            </el-col>
            <el-col :span="6">
              <div class="label">视频封面：</div>
              <div class="content" v-if="orderVideoUploadLinkSimpleVO?.videoCover">
                <el-button
                  link
                  type="primary"
                  style="margin-top: -5px"
                  @click="showViewer([orderVideoUploadLinkSimpleVO?.videoCover])"
                >
                  查看
                </el-button>
              </div>
              <span v-else>-</span>
            </el-col>
            <el-col :span="6">
              <div class="label">上传账号：</div>
              <div class="content">
                {{ orderVideoUploadLinkSimpleVO?.uploadAccount }}
              </div>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <div class="label">上传备注：</div>
              <div class="content" style="word-break: break-all; line-break: anywhere">
                {{ orderVideoUploadLinkSimpleVO?.remark || '-' }}
              </div>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <div class="label">实际上传链接：</div>
              <div
                class="content more-ell link-btn"
                @click="openLink(orderVideoUploadLinkSimpleVO?.uploadLink)"
              >
                {{ orderVideoUploadLinkSimpleVO?.uploadLink }}
              </div>
            </el-col>
          </el-row>
          <!-- <el-row>
            <el-col :span="8">
              <div class="label">上传链接：</div>
              <div
                class="content more-ell link-btn"
                @click="openLink(orderVideoUploadLinkSimpleVO?.needUploadLink)"
              >
                {{ orderVideoUploadLinkSimpleVO?.needUploadLink }}
              </div>
            </el-col>
            <el-col :span="8">
              <div class="label">提交时间：</div>
              <div class="content">
                {{ orderVideoUploadLinkSimpleVO?.time }}
              </div>
            </el-col>
            
          </el-row> -->
          <el-divider style="margin: 0 0 10px 0" />
        </div>
        <div>
          <div style="margin-bottom: 10px" v-if="orderFeedBackSimpleVOS && orderFeedBackSimpleVOS.length > 0">
            反馈商家的素材
            <el-button
              v-btn
              type="warning"
              size="small"
              v-if="orderVideoSimpleVO?.afterSaleTaskStatus == 1"
              @click="toTaskPage('1', orderVideoSimpleVO?.videoCode)"
            >
              售后中
            </el-button>
            <el-button
              v-btn
              type="warning"
              size="small"
              v-if="orderVideoSimpleVO?.workOrderTaskStatus === 1"
              @click="toTaskPage('2', orderVideoSimpleVO?.videoCode)"
            >
              工单中
            </el-button>
            <el-button
              v-btn
              type="warning"
              size="small"
              v-if="orderVideoSimpleVO?.workOrderTaskStatus === 4"
              @click="toTaskPage('2', orderVideoSimpleVO?.videoCode)"
            >
              工单已完结
            </el-button>
            <el-button
              v-btn
              type="warning"
              size="small"
              v-if="orderVideoSimpleVO?.afterSaleTaskStatus == 4"
              @click="toTaskPage('1', orderVideoSimpleVO?.videoCode)"
            >
              售后已完结
            </el-button>
          </div>
          <template v-for="(item, index) in orderFeedBackSimpleVOS" :key="index">
            <el-row>
              <el-col :span="8">
                <div class="label">素材链接：</div>
                <div class="content link-btn" style="display: flex; align-items: baseline">
                  <span @click="openLink(item?.url)" style="cursor: pointer" class="more-ell">
                    {{ item.url }}
                  </span>
                  <el-tag v-if="item.rollbackId" style="margin-left: 5px" type="danger" round size="small">
                    回退订单
                  </el-tag>
                </div>
              </el-col>
              <el-col :span="2">
                <div style="margin-left: 50px">{{ item.type == '1' ? '视频' : '照片' }}</div>
              </el-col>
              <el-col :span="4">
                <div class="label">提交人：</div>
                <div class="content">
                  {{ item.createUser?.name }}
                </div>
              </el-col>
              <el-col :span="6" style="flex-wrap: wrap">
                <template
                  v-if="item.orderVideoTaskDetailSimpleVOS && item.orderVideoTaskDetailSimpleVOS.length > 0"
                >
                  <template v-for="(data, index) in item.orderVideoTaskDetailSimpleVOS">
                    <div
                      v-if="index <= 2"
                      style="color: #409eff; margin-left: 5px; cursor: pointer"
                      @click="handleToShowVideo(data)"
                    >
                      {{ handleShowType(data) }}
                    </div>
                    <div
                      v-if="index > 2 && item.isShowMoreTaskDetail"
                      style="color: #409eff; margin-left: 5px; cursor: pointer"
                      @click="handleToShowVideo(data)"
                    >
                      {{ handleShowType(data) }}
                    </div>
                  </template>
                  <el-button
                    v-btn
                    type="primary"
                    v-if="item.orderVideoTaskDetailSimpleVOS.length > 3"
                    link
                    @click="toShowMoreTaskDetail(item)"
                  >
                    {{ item.isShowMoreTaskDetail ? '收起' : '更多' }}
                  </el-button>
                </template>
              </el-col>
              <el-col :span="4">
                <div class="label">提交时间：</div>
                <div class="content">
                  {{ item.createTime }}
                </div>
              </el-col>
            </el-row>
          </template>

          <div
            style="margin-bottom: 10px"
            v-if="orderFeedBackMaterialInfoSimpleVOS && orderFeedBackMaterialInfoSimpleVOS.length > 0"
          >
            模特反馈的素材
          </div>
          <template v-for="(item, index) in orderFeedBackMaterialInfoSimpleVOS" :key="index">
            <el-row>
              <el-col :span="8">
                <div class="label">素材链接：</div>
                <div class="content link-btn" style="display: flex; align-items: baseline">
                  <span class="more-ell" style="cursor: pointer" @click="openLink(item?.link)">
                    {{ item.link }}
                  </span>
                  <el-tag v-if="item.rollbackId" style="margin-left: 5px" type="danger" round size="small">
                    回退订单
                  </el-tag>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="label">提交人：</div>
                <div class="content">
                  {{ item.object == '2' ? item.back?.name : item.model?.name }}
                </div>
              </el-col>
              <el-col :span="8">
                <div class="label">提交时间：</div>
                <div class="content">
                  {{ item.uploadTime }}
                </div>
              </el-col>
            </el-row>
          </template>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { workOrderTypeList, afterSaleVideoTypeList, afterSalePicTypeList } from '@/views/task/data.js'

// import { picCountOptions, videoFormatOptions } from '@/views/order/list/data.js'
// import { payTypeMap } from '@/utils/dict'
import { uploadStatusV1 } from '@/views/task/clip/data.js'
import { useViewer } from '@/hooks/useViewer'
import { getTranslate } from '@/api/order/order'
const { showViewer } = useViewer()

const router = useRouter()

// const { proxy } = getCurrentInstance()

const props = defineProps({
  data: {
    type: Object,
    default: () => ({}),
  },
})

const emits = defineEmits(['showHistoryRecord'])

const orderVO = computed(() => props.data.orderVO)
const orderVideoVO = computed(() => props.data.orderVideoVO)
const orderVideoUploadLinkSimpleVO = computed(() => props.data.orderVideoUploadLinkSimpleVO)
const orderFeedBackMaterialInfoSimpleVOS = computed(() => props.data.orderFeedBackMaterialInfoSimpleVOS)
const orderFeedBackSimpleVOS = computed(() => {
  let tempList = props.data.orderFeedBackSimpleVOS || []
  if (props.data.orderFeedBackSimpleVOS && props.data.orderFeedBackSimpleVOS.length > 0) {
    tempList.forEach(item => {
      item.isShowMoreTaskDetail = false
    })
  }
  return tempList
})

const orderVideoSimpleVO = computed(() => props.data.orderVideoSimpleVO)
// const sList = computed(() => {
//   return orderVideoUploadLinkSimpleVO?.object == '1'? orderVideoUploadLinkSimpleVO?.company : orderVideoUploadLinkSimpleVO?.back
// })

const demandsTips = computed(() => {
  if (props.data?.orderVideoVO?.shootRequired) {
    return props.data.orderVideoVO.shootRequired.some(item => {
      return item.createTime !== item.updateTime
    })
  }
  return false
})

const sList = [1, 2]
const mList = [1, 2]

function handleShowType(data) {
  let str = ''
  if (!data) return ''

  if (data.afterSaleClass == 1) {
    str = afterSaleVideoTypeList.find(item => item.value == data.afterSaleVideoType)?.label
  } else if (data.afterSaleClass == 2) {
    str = afterSalePicTypeList.find(item => item.value == data.afterSalePicType)?.label
  } else if (orderVideoSimpleVO.value.workOrderTaskStatus) {
    str = workOrderTypeList.find(item => item.value == data.workOrderType)?.label
  }

  return str
}

function handleToShowVideo(data) {
  let type = '1'
  let afterType = ''
  if (data.afterSaleClass == 1) {
    afterType = data.afterSaleVideoType
    type = '1'
  } else if (data.afterSaleClass == 2) {
    if (data.afterSalePicType == 1) {
      afterType = 11
    } else if (data.afterSalePicType == 2) {
      afterType = 12
    } else if (data.afterSalePicType == 3) {
      afterType = 13
    } else if (data.afterSalePicType == 4) {
      afterType = 14
    }
    type = '1'
  } else if (orderVideoSimpleVO.value.workOrderTaskStatus) {
    afterType = data.workOrderType
    type = '2'
  }

  sessionStorage.setItem(
    'taskQuery',
    JSON.stringify({ type: type, vc: orderVideoSimpleVO.value.videoCode, afterType })
  )
  const path = `/task/workOrder?type=${type}&keyword=${orderVideoSimpleVO.value.videoCode}&afterType=${afterType}`
  // const { href } = router.resolve({ path })
  window.open(path, '_blank')
}

//触发历史变更记录
function doShowHistoryRecord() {
  emits('showHistoryRecord')
}

//翻译拍摄要求
const transformContentList = ref([])
const translateLoading = ref(false)
function doTranslate() {
  translateLoading.value = true
  let data = []
  if (orderVideoVO.value?.shootRequired) {
    orderVideoVO.value.shootRequired.forEach((item, index) => {
      data.push(item.content)
    })
  }
  getTranslate({ language: 1, wordList: data })
    .then(res => {
      transformContentList.value = res.data
      translateLoading.value = false
    })
    .finally(() => (translateLoading.value = false))
}

function openLink(url) {
  window.open(url, '_blank')
}

function openHistoryUpload(id) {
  emits('showHistoryUploadRecord', id)
}
function openHistoryClip() {
  emits('showHistoryClipRecord')
}

const toShowMoreTaskDetail = data => {
  data.isShowMoreTaskDetail = !data.isShowMoreTaskDetail
}

function toTaskPage(type, videoCode) {
  const path = `/task/workOrder?type=${type}&keyword=${videoCode}`
  window.open(path, '_blank')
}
</script>

<style scoped lang="scss">
.product-info {
  margin-top: 20px;
  background-color: #fff;
  //   padding: 13px;
  border-radius: 4px;
  gap: 10px;
  position: relative;
  box-shadow: var(--el-box-shadow-light);
  border: 1px solid #e9e9e9;
}
.info-head {
  padding: 13px;
  background: #f9f9f9;
  border-bottom: 1px solid #e9e9e9;
}
.info-content {
  padding: 10px 20px;
  &-right {
    flex: 1;
  }
  &-left {
    margin-right: 30px;
    .icon {
      cursor: pointer;
      object-fit: fill;
      width: 100px;
      height: 100px;
    }
  }
}
.video-content {
  padding: 10px 20px;
}
.order-content {
  padding: 10px 20px;
  .label {
    // text-align: end;
    // width: 100px !important;
    // margin-right: 20px;
  }
}

.link-btn {
  color: #409eff;
}
.el-col {
  align-items: baseline;
}

.el-col-8,
.el-col-7,
.el-col-6,
.el-col-2,
.el-col-4,
.el-col-24 {
  display: flex;
  margin-bottom: 10px;
  // align-items: center;
  font-size: 13px;
  .label {
    flex-shrink: 0;
    width: 100px;
    color: #999;
    text-align: end;
    margin-right: 20px;
  }
  .content {
    flex-shrink: 0;
    max-width: calc(100% - 120px);
    word-break: break-all;
  }
}
.el-col-12 {
  display: flex;
  margin-bottom: 10px;
  font-size: 13px;
  .label {
    flex-shrink: 0;
    max-width: 100px;
    color: #999;
    text-align: end;
    margin-right: 20px;
  }
  .content {
    flex-shrink: 0;
    width: calc(100% - 100px);
    word-break: break-all;
  }
}
.col-24 {
  align-items: flex-start;
  margin-bottom: 10px;
  width: 100%;
  font-size: 13px;
  .label {
    flex-shrink: 0;
    max-width: 100px;
    color: #999;
    text-align: end;
    margin-right: 20px;
  }
  .content {
    flex-shrink: 0;
    width: calc(100% - 100px);
    word-break: break-all;
  }
  .tip {
    flex-shrink: 0;
    color: #7f7f7f;
  }
}
.more-box {
  max-height: 0;
  overflow: hidden;
  transition: 0.5s;

  &.maxHeight {
    max-height: 400px;
  }
}
</style>
