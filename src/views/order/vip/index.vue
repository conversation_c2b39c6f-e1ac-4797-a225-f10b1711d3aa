<template>
  <el-container>
    <el-main>
      <ElTablePage
        ref="tableRef"
        :columns="columns"
        :data="pageData"
        :loading="tableLoading"
        :currentPage="currentPage"
        :pageSize="pageSize"
        :total="total"
        :tableAction="{
          width: '190',
          fixed: 'right',
        }"
        @page-change="pageChange"
        row-key="id"
      >
        <template #tableHeader>
          <div>
            <el-form :inline="true" :model="queryParams" class="demo-form-inline" @submit.prevent>
              <el-form-item label="搜索" prop="id">
                <el-input
                  v-model="queryParams.val"
                  clearable
                  style="max-width: 300px"
                  placeholder="请输入对应的值"
                >
                  <template #prepend>
                    <el-select
                      v-model="queryParams.select"
                      placeholder="请选择"
                      clearable
                      style="width: 120px"
                    >
                      <el-option
                        v-for="dict in searchType"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                      />
                    </el-select>
                  </template>
                </el-input>
              </el-form-item>
              <el-form-item label="套餐类型" prop="packageType">
                <el-select
                  v-model="queryParams.packageType"
                  placeholder="请选择"
                  style="width: 150px"
                  clearable
                >
                  <el-option
                    v-for="dict in setMealTypeList"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="状态" prop="status">
                <el-select
                  v-model="queryParams.statusList"
                  placeholder="请选择"
                  style="width: 150px"
                  clearable
                  multiple
                  collapse-tags
                  collapse-tags-tooltip
                >
                  <el-option
                    v-for="dict in vipOrderStatus"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="支付方式" prop="type">
                <el-select
                  v-model="queryParams.payTypes"
                  placeholder="请选择"
                  style="width: 180px"
                  clearable
                  multiple
                  collapse-tags
                  collapse-tags-tooltip
                >
                  <el-option
                    v-for="data in payTypeSelectList"
                    :key="data.value"
                    :label="data.label"
                    :value="data.value"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="购买标识">
                <el-select
                  v-model="queryParams.isFirstBuy"
                  placeholder="请选择"
                  style="width: 180px"
                  clearable
                >
                  <el-option :value="1" label="首次购买">首次购买</el-option>
                  <el-option :value="2" label="续费购买">续费购买</el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="下单时间">
                <el-date-picker
                  v-model="queryParams.orderTime"
                  format="YYYY/M/D HH:mm:ss"
                  value-format="YYYY-MM-DD HH:mm:ss"
                  type="datetimerange"
                  range-separator="-"
                  unlink-panels
                  start-placeholder="开始时间"
                  end-placeholder="结束时间"
                  :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 2, 1, 23, 59, 59)]"
                  :shortcuts="shortcuts"
                ></el-date-picker>
              </el-form-item>
              <el-form-item label="支付时间">
                <el-date-picker
                  v-model="queryParams.payTime"
                  format="YYYY/M/D HH:mm:ss"
                  value-format="YYYY-MM-DD HH:mm:ss"
                  type="datetimerange"
                  range-separator="-"
                  unlink-panels
                  start-placeholder="开始时间"
                  end-placeholder="结束时间"
                  :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 2, 1, 23, 59, 59)]"
                  :shortcuts="shortcuts"
                ></el-date-picker>
              </el-form-item>
              <el-form-item label="优惠类型">
                <el-select
                  v-model="queryParams.promotionActivityTypes"
                  placeholder="请选择"
                  style="width: 200px"
                  multiple
                  collapse-tags
                  collapse-tags-tooltip
                  clearable
                >
                  <el-option
                    v-for="dict in promotionActivityTypeList"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
              <el-form-item>
                <div>
                  <el-button v-btn type="primary" icon="Search" native-type="submit" @click="onQuery">
                    搜索
                  </el-button>
                  <el-button v-btn icon="Refresh" @click="resetQuery">重置</el-button>
                </div>
              </el-form-item>
              <div style="margin-bottom: 10px">实际支付金额(CNY)：￥{{ totalRealPayAmount }}</div>
            </el-form>
          </div>
        </template>
        <template #orderUser="{ row }">
          <div style="text-align: left">
            <div>公司名称：{{ row.businessName || '-' }}</div>
            <div>微信名：{{ row.nickName || '-' }}</div>
          </div>
          <!-- <div v-if="row.businessAccount">(ID: {{ row.businessAccount }})</div> -->
        </template>
        <template #wechatChannelType="{ row }">
          <div>
            <span
              v-if="row.wechatChannelType == 3 || row.wechatChannelType == 4 || row.wechatChannelType == 6"
            >
              {{ originList.find(item => item.value == row.wechatChannelType)?.label || '-' }}
            </span>
            <span v-else-if="row.wechatChannelType == 1 || row.wechatChannelType == 2">
              {{ originList.find(item => item.value == row.wechatChannelType)?.label || '-' }}-{{
                row.wechatChannelStringType || '-'
              }}
            </span>
            <span v-else-if="row.wechatChannelType == 7">
              {{ originList.find(item => item.value == row.wechatChannelType)?.label || '-' }}-{{
                row.wechatChannelStringType || '-'
              }}
            </span>
            <span v-else>-</span>
          </div>
          <div>
            <span>{{ row.addWechatTime || '-' }}</span>
          </div>
        </template>
        <template #orderInfo="{ row }">
          <div>
            <el-tag
              v-if="row.isFirstBuy"
              style="position: absolute; top: 0; left: 0"
              type="warning"
              size="small"
            >
              {{ row.isFirstBuy == 1 ? '首次购买' : '续费购买' }}
            </el-tag>
            <div>
              {{ row.orderNum }}
            </div>
          </div>
          <div>下单时间: {{ row.orderTime }}</div>
        </template>
        <template #packageType="{ row }">
          <div>
            {{ getPackageTypeLabel(row.packageType) }}
          </div>
        </template>
        <template #discountDetail="{ row }">
          <div
            style="text-align: left"
            v-if="row.orderDiscountDetailVOS && row.orderDiscountDetailVOS.length > 0"
          >
            <template v-if="row.orderDiscountDetailVOS[0].channelType == 7">
              <el-tag style="position: absolute; top: 0; right: 0" size="small">
                {{
                  memberStatusVipOrderSpecialList.find(item => item.value == row.seedMemberStatus)?.label ||
                  memberStatusVipOrderSpecialList[0].label
                }}
              </el-tag>
            </template>

            <div>
              优惠类型：{{
                disountTypeList.find(item => item.value == row.orderDiscountDetailVOS[0].type)?.label
              }}
            </div>
            <div v-if="row.orderDiscountDetailVOS[0].type == 3 || row.orderDiscountDetailVOS[0].type == 5">
              {{ row.orderDiscountDetailVOS[0].channelType == 7 ? '裂变' : '渠道' }}名称：{{
                row.orderDiscountDetailVOS[0].channelName
              }}{{ row.seedId ? ` (ID${row.seedId})` : '' }}
            </div>
            <div>
              会员折扣：{{ row.orderDiscountDetailVOS[0].discountRatio
              }}{{ row.memberDiscountType == 1 ? '元 (固定金额)' : '% (固定比例)' }}
            </div>
            <div>优惠金额：￥{{ row.orderDiscountDetailVOS[0].discountAmount }}</div>
          </div>
          <span v-else>-</span>
        </template>
        <template #packagePrice="{ row }">
          <div style="text-align: left">
            <div>美元：${{ row.packageAmount }}</div>
            <div>百度汇率：{{ row.currentExchangeRate || '-' }}</div>
            <div>人民币：￥{{ row.orderAmount }}</div>
          </div>
        </template>
        <template #seedCodeDiscount="{ row }">
          <div v-if="row.seedCodeDiscount" style="text-align: left">
            <div>{{ row.channelType == 7 ? '裂变' : '渠道' }}名称：{{ row.channelName }}</div>
            <div>会员折扣：{{ row.settleRage }}%</div>
            <div>优惠金额：￥{{ row.seedCodeDiscount }}</div>
          </div>
          <div v-else>-</div>
        </template>
        <template #payAmount="{ row }">
          <div style="text-align: left">
            <div v-if="row.useBalance">钱包余额抵扣：￥{{ row.useBalance }}</div>
            <div>剩余支付：￥{{ row.surplusAmount }}</div>
            <div>
              实付金额：
              <span v-if="row.auditStatus == 1">
                <template v-if="row.realPayAmountCurrency == 0 || row.realPayAmountCurrency">
                  {{ row.realPayAmountCurrency }}
                </template>
                <template v-else>-</template>
              </span>
              <span v-else>-</span>
            </div>
            <div>支付币种： {{ handleCurrency(row.currency) }}</div>
            <div v-if="row.payType == 7 || row.payType == 17">
              实付人民币：{{ row.auditStatus == 1 ? row?.realPayAmount : '-' }}
            </div>
          </div>
        </template>
        <template #payWay="{ row }">
          <div v-if="row.status != 4 && row.payType">
            {{ payTypeMap[row.payType] }}
          </div>
          <div v-else>-</div>
        </template>
        <template #payStatus="{ row }">
          <div v-if="row.status">
            <div>
              {{ getVipOrderStatusText(row.status) }}
            </div>
            <div v-if="row.status == 3">
              {{ row.payTime }}
            </div>
          </div>
        </template>
        <template #tableAction="{ row }">
          <div class="column-flex-button">
            <el-button
              v-if="row.isDefaultExchangeRate && row.status != '4'"
              v-btn
              link
              type="primary"
              @click="changeRateDialog(row.orderNum)"
              v-hasPermi="['order:vip:rate']"
            >
              汇率异常调整
            </el-button>
            <el-button
              v-btn
              v-if="row.status == 1 || row.status == 2"
              v-hasPermi="['order:vip:cancel']"
              link
              type="primary"
              @click="cancelOrder(row)"
            >
              取消订单
            </el-button>
            <!-- <el-button v-btn link type="primary" @click="viewDetail(row)">查看详情</el-button> -->
            <el-button
              v-hasPermi="['order:vip:details']"
              v-btn
              link
              type="primary"
              @click="routerNewWindow('/order/details/member/' + row.orderNum)"
            >
              查看详情
            </el-button>
          </div>
        </template>
        <template #pageLeft>
          <DownloadBtn
            v-hasPermi="['order:vip:export']"
            type="success"
            plain
            icon="Download"
            url="/order/order/backend/export/member/list"
            :params="getQueryParams('export')"
            fileName="会员订单.xlsx"
          />
        </template>
      </ElTablePage>
    </el-main>
    <ChangeRate ref="ChangeRateRef" @success="handleQuery" :requestApi="updateBaiduRate" />
  </el-container>
</template>

<script setup>
import ChangeRate from '@/views/order/components/dialog/changeRate.vue'
import ElTablePage from '@/components/Table/ElTablePage.vue'
import DownloadBtn from '@/components/Button/DownloadBtn.vue'
import {
  getVipOrderStatusText,
  disountTypeList,
  promotionActivityTypeList,
  memberStatusVipOrderSpecialList,
} from '@/views/order/vip/data.js'
import { vipOrderStatus, originList } from '@/views/order/vip/data.js'
import { vipList, cancelMemberOrder } from '@/api/finance/vip.js'
import { setMealTypeList } from '@/views/finance/data.js'
import { payTypeSelectList, payTypeMap } from '@/utils/dict'
import { ElLoading, ElMessage, ElMessageBox } from 'element-plus'
import { updateBaiduRate } from '@/api/order/order'

const { proxy } = getCurrentInstance()
const { sys_money_type } = proxy.useDict('sys_money_type')

const router = useRouter()
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const tableLoading = ref(false)
const queryParams = ref({
  val: '',
  select: 'orderNum',
  isFirstBuy: '',
  payTypes: [],
  statusList: [],
  orderTime: [],
  payTime: [],
  payType: '',
  packageType: '',
  promotionActivityTypes: [],
})
const pageData = ref([])

const ChangeRateRef = ref(null)

const shortcuts = [
  {
    text: '今天',
    value: () => {
      const start = new Date()
      start.setHours(0, 0, 0, 0)
      const end = new Date()
      end.setHours(23, 59, 59, 59)
      return [start, end]
    },
  },
]

// 会员订单号、商家微信昵称、商家账号、会员编码
const searchType = ref([
  { label: '会员订单号', value: 'orderNum' },
  { label: '商家微信昵称', value: 'nickName' },
  // { label: '商家账号', value: 'businessAccount' },
  { label: '会员编码', value: 'memberCode' },
])

function pageChange(page) {
  currentPage.value = page.currentPage
  pageSize.value = page.pageSize
  handleQuery()
}

function getPackageTypeLabel(value) {
  return setMealTypeList.find(item => item.value === value).label
}

function getPackageTypePrice(value) {
  return setMealTypeList.find(item => item.value === value).price
}

const columns = [
  { slot: 'orderUser', label: '下单商家', width: '180' },
  { prop: 'memberCode', label: '会员编码', width: '150', handle: data => data || '-' },
  // {
  //   prop: 'phone',
  //   label: '下单账号',
  //   width: '160',
  //   handle: (data, row) => {
  //     let str = ''
  //     if (row.nickName) {
  //       str += row.nickName + '\n'
  //     }
  //     if (data) {
  //       str += data
  //     }
  //     return str
  //   },
  // },
  { slot: 'orderInfo', label: '单号信息', minWidth: '250' },
  { slot: 'packageType', label: '套餐类型', width: '120' },
  { slot: 'discountDetail', label: '优惠信息', width: '230' },
  {
    prop: 'presentedTime',
    label: '赠送活动',
    width: '150',
    handle: (data, row) => {
      if (data) {
        let s =
          row.presentedTimeType == 1
            ? '天'
            : row.presentedTimeType == 2
            ? '月'
            : row.presentedTimeType == 3
            ? '年'
            : '-'
        return `赠送${data || '-'}${s}`
      } else {
        return '-'
      }
    },
  },
  { slot: 'packagePrice', label: '套餐金额', width: '200' },
  { slot: 'wechatChannelType', label: '添加企微来源', width: '200' },
  {
    prop: 'connectUserName',
    label: '售前',
    width: '200',
    handle: (data, row) => {
      return data ? data : '-'
    },
  },
  { slot: 'payWay', label: '支付方式', minWidth: '170' },
  { slot: 'payAmount', label: '支付情况', width: '200' },
  { slot: 'payStatus', label: '状态', width: '200' },
]

handleQuery()

function getQueryParams(type) {
  let { orderTime, payTime, select, val, ...params } = queryParams.value
  if (!type) {
    params.pageNum = currentPage.value
    params.pageSize = pageSize.value
  }

  if (orderTime && orderTime.length) {
    params.orderTimeBegin = queryParams.value.orderTime[0]
    params.orderTimeEnd = queryParams.value.orderTime[1]
  }
  if (payTime && payTime.length) {
    params.payTimeBegin = queryParams.value.payTime[0]
    params.payTimeEnd = queryParams.value.payTime[1]
  }
  if (select) {
    params[select] = val
  }
  if (params.payTypes.length > 0) {
    params.payTypes = params.payTypes.join(',')
  }
  if (params.statusList.length > 0) {
    params.statusList = params.statusList.join(',')
  }
  return params
}

function onQuery() {
  currentPage.value = 1
  handleQuery()
}
function handleQuery() {
  getList(getQueryParams())
}

function resetQuery() {
  queryParams.value = {
    val: '',
    select: 'orderNum',
    payTypes: [],
    statusList: [],
    orderTime: [],
    payTime: [],
    payType: '',
    packageType: '',
    isFirstBuy: '',
    promotionActivityTypes: [],
  }
  handleQuery()
}

const totalRealPayAmount = ref(0)
function getList(param) {
  tableLoading.value = true
  vipList(param)
    .then(res => {
      pageData.value = res.data.rows
      totalRealPayAmount.value = res.data.totalAmount
      total.value = res.data.total
    })
    .finally(() => (tableLoading.value = false))
}

function handleCurrency(val) {
  if (val == '1') {
    return '人民币'
  }
  return sys_money_type.value.find(item => item.value == val)?.label || '-'
}

//汇率异常调整
function changeRateDialog(orderNum) {
  ChangeRateRef.value.open(orderNum)
}

function cancelOrder(data) {
  ElMessageBox.confirm('确认取消订单？', '温馨提示', {
    autofocus: false,
    confirmButtonText: '确定',
    cancelButtonText: '再想想',
  })
    .then(() => {
      const el_loading = ElLoading.service({
        lock: true,
        text: '正在执行中，请稍后',
        background: 'rgba(0, 0, 0, 0.7)',
      })
      cancelMemberOrder(data.orderNum)
        .then(res => {
          ElMessage.success('取消成功')
          handleQuery()
        })
        .finally(() => el_loading.close())
    })
    .catch(() => {})
}

function viewDetail(data) {
  router.push({
    path: '/order/details/member/' + data.orderNum,
    params: data,
  })
}
function routerNewWindow(path) {
  const { href } = router.resolve({ path })
  window.open(href, '_blank')
}
</script>

<style lang="scss">
.column-flex-button {
  display: flex;
  flex-direction: column;

  .el-button {
    margin-left: 0;
  }
}
</style>
