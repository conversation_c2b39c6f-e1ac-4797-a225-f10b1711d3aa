// 1待支付、2待审核、3交易成功、4交易关闭
export const vipOrderStatus = [
  { label: '待支付', value: 1 },
  { label: '待审核', value: 2 },
  { label: '交易成功', value: 3 },
  { label: '交易关闭', value: 4 },
]
export function getVipOrderStatusText(value) {
  const status = vipOrderStatus.find(item => item.value === value)
  return status ? status.label : ''
}

export const originList = [
  // { label: '普通渠道', value: 0 },
  { label: 'SC', value: 1 },
  { label: 'FX', value: 2 },
  { label: '子账号', value: 3 },
  { label: '官网', value: 4 },
  { label: 'LB', value: 7 },
  { label: 'VIP介绍页', value: 6 },
]

export const disountTypeList = [
  { label: '满5单减100', value: 1 },
  { label: '半价续费', value: 2 },
  { label: '渠道优惠', value: 3 },
  { label: '裂变优惠', value: 5 },
]

export const promotionActivityTypeList = [
  { label: '裂变优惠', value: 5 },
  { label: '渠道优惠', value: 3 },
  { label: '半价续费', value: 2 },
  { label: '无优惠', value: -1 },
]

// 注意： 这里即将过期的也显示正常状态
export const memberStatusVipOrderSpecialList = [
  { label: '非会员裂变', value: 0 },
  { label: '会员裂变', value: 1 },
  { label: '会员裂变', value: 2 },
  { label: '非会员裂变', value: 3 },
]