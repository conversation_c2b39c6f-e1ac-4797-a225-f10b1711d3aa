<template>
  <el-dialog
    v-model="dialogVisible"
    align-center
    title="历史变更记录"
    width="900px"
    @close="handleClose"
    :close-on-press-escape="false"
  >
    <div
      class="history-box"
      v-loading="loading"
      v-if="historyRecordList.length && historyRecordList.length > 0"
    >
      <div>
        <el-steps style="" :active="activeTab" simple>
          <template v-for="(item, index) in historyRecordList" :key="item.id">
            <el-step>
              <template #icon></template>
              <template #title>
                <div @click="handleClick(index)" style="cursor: pointer">
                  <span>
                    {{
                      item.logType == 4
                        ? '审核订单时修改'
                        : item.logType == 3
                        ? '商家同意后修改'
                        : item.logType == 2
                        ? '编辑订单时修改'
                        : '初始记录'
                    }}
                    <!-- {{ logTypeList.find(data => data.value == item.logType)?.label || '-' }} -->
                  </span>
                  <span v-if="item.rollbackId" style="font-size: 12px; color: #848282">(订单回退)</span>
                  <div style="color: black">{{ item.changeTime }}</div>
                </div>
              </template>
            </el-step>
          </template>
        </el-steps>
      </div>
      <div class="edit-head" v-if="selectRecordInfo.logType != 1">
        <div class="edit-head__title">修改内容：</div>
        <div class="edit-head__content">
          <span>修改人：{{ selectRecordInfo.changeUserName }}({{ selectRecordInfo.changeUserId }})</span>
          <span style="margin-left: 50px">修改时间：{{ selectRecordInfo.changeTime }}</span>
        </div>
      </div>
      <el-divider v-if="selectRecordInfo.logType != 1" style="margin: 0; border-top: 1px solid #f2f2f2" />
      <div class="edit-content" style="max-height: 400px; overflow: auto" v-if="!selectRecordInfo.isNothing">
        <el-row>
          <el-col :span="8" v-if="selectRecordInfo.platform || selectRecordInfo.platform == '0'">
            <div class="label">使用平台：</div>
            <div class="content">
              <biz-model-platform :value="selectRecordInfo?.platform" tag="text" />
            </div>
          </el-col>
          <el-col
            :span="8"
            v-if="selectRecordInfo.shootingCountry || selectRecordInfo.shootingCountry == '0'"
          >
            <div class="label">拍摄国家：</div>
            <div class="content">
              <biz-nation :value="selectRecordInfo?.shootingCountry" tag="text" />
            </div>
          </el-col>
          <el-col :span="8" v-if="selectRecordInfo.modelType || selectRecordInfo.modelType == '0'">
            <div class="label">模特类型：</div>
            <div class="content">
              <biz-model-type :value="selectRecordInfo?.modelType" tag="text" />
            </div>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8" v-if="selectRecordInfo.videoFormat">
            <div class="label">视频格式：</div>
            <div class="content">
              <template v-for="item in videoFormatOptions" :key="item.value">
                <span v-if="item.value == selectRecordInfo?.videoFormat">{{ item.label }}</span>
              </template>
            </div>
          </el-col>
          <el-col :span="8" v-if="selectRecordInfo.intentionModelName">
            <div class="label">意向模特：</div>
            <div class="content">
              {{ selectRecordInfo.intentionModelName }} (ID:{{ selectRecordInfo.intentionModelId }})
            </div>
          </el-col>
          <el-col :span="8" v-if="selectRecordInfo.videoDuration" style="align-items: baseline">
            <div class="label">视频时长：</div>
            <div class="content">
              {{ selectRecordInfo?.videoDuration ? selectRecordInfo?.videoDuration + 's' : '' }}
            </div>
          </el-col>
        </el-row>
        <el-row>
          <el-col
            :span="8"
            v-if="selectRecordInfo.videoStyle || selectRecordInfo.videoStyle === 0"
            style="align-items: baseline"
          >
            <div class="label">视频风格：</div>
            <div class="content">
              {{ videoStyleList.find(item => item.value == selectRecordInfo.videoStyle)?.label }}
            </div>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8" v-if="selectRecordInfo.productPic || selectRecordInfo.productPic == null">
            <div class="label">产品图：</div>
            <div class="content">
              <template v-if="selectRecordInfo.productPic != null">
                <ViewerImageList
                  :data="[selectRecordInfo.productPic]"
                  is-preview-all
                  :show-delete-btn="false"
                />
              </template>
              <div v-else>-</div>
            </div>
          </el-col>
          <el-col
            :span="16"
            v-if="
              (selectRecordInfo.referencePic && selectRecordInfo.referencePic.length > 0) ||
              selectRecordInfo.referencePic == null
            "
          >
            <div class="label">参考图片：</div>
            <div class="content">
              <template v-if="selectRecordInfo.referencePic != null">
                <ViewerImageList
                  :data="selectRecordInfo.referencePic"
                  is-preview-all
                  :show-delete-btn="false"
                />
              </template>
              <div v-else>-</div>
            </div>
            <ViewerImageList />
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12" v-if="selectRecordInfo.productCategoryName">
            <div class="label">产品品类：</div>
            <div class="content">{{ selectRecordInfo.productCategoryName }}</div>
          </el-col>
          <el-col :span="12" v-if="selectRecordInfo.isGund >= 0">
            <div class="label">通品：</div>
            <div class="content">{{ selectRecordInfo.isGund ? '是' : '否' }}</div>
          </el-col>
        </el-row>
        <el-row>
          <div class="flex-start col-24" v-if="selectRecordInfo.productChinese">
            <div class="label">中文名称：</div>
            <div class="content">{{ selectRecordInfo.productChinese }}</div>
          </div>
        </el-row>
        <el-row>
          <div class="flex-start col-24" v-if="selectRecordInfo.productEnglish">
            <div class="label">英文名称：</div>
            <div class="content more-ell">{{ selectRecordInfo.productEnglish }}</div>
          </div>
        </el-row>
        <el-row>
          <div class="flex-start col-24" v-if="selectRecordInfo.productLink">
            <div class="label">产品链接：</div>
            <div class="content more-ell">{{ selectRecordInfo?.productLink || '-' }}</div>
          </div>
        </el-row>
        <el-row>
          <div class="flex-start col-24" v-if="selectRecordInfo.referenceVideoLink">
            <div class="label">参考视频：</div>
            <div class="content more-ell">{{ selectRecordInfo?.referenceVideoLink || '-' }}</div>
          </div>
        </el-row>
        <el-row>
          <div class="flex-start col-24" v-if="selectRecordInfo.sellingPointProduct">
            <div class="label">产品卖点：</div>
            <div class="content">{{ selectRecordInfo?.sellingPointProduct }}</div>
          </div>
        </el-row>
        <el-row>
          <div
            class="flex-start col-24"
            v-if="selectRecordInfo.clipsRequired && selectRecordInfo.clipsRequired.length > 0"
          >
            <div class="label">剪辑要求：</div>
            <div class="content">
              <div v-for="(item, index) in selectRecordInfo.clipsRequired" :key="index">
                {{ handleR(item.content) }}
              </div>
            </div>
          </div>
        </el-row>
        <el-row>
          <div
            class="flex-start col-24"
            v-if="selectRecordInfo.shootRequired && selectRecordInfo.shootRequired.length > 0"
          >
            <div class="label">拍摄建议：</div>
            <div class="content">
              <div v-for="(item, index) in selectRecordInfo.shootRequired" :key="index">
                {{ handleR(item.content) }}
              </div>
            </div>
          </div>
        </el-row>
        <el-row>
          <div
            class="flex-start col-24"
            v-if="
              (selectRecordInfo?.cautions && selectRecordInfo.cautions.length > 0) ||
              selectRecordInfo.cautions == null
            "
          >
            <div class="label" style="text-align: start">模特要求：</div>
            <div class="content">
              <template v-if="selectRecordInfo.cautions != null">
                <div v-for="(item, index) in selectRecordInfo.cautions" :key="index">
                  {{ handleR(item.content) }}
                </div>
              </template>
              <div v-else>-</div>
            </div>
          </div>
        </el-row>
        <el-row>
          <div v-if="selectRecordInfo.cautionsPics != null">
            <ViewerImageList :data="selectRecordInfo?.cautionsPics" is-preview-all :show-delete-btn="false" />
          </div>
        </el-row>
        <el-row>
          <div
            class="flex-start col-24"
            v-if="
              selectRecordInfo.orderSpecificationRequire &&
              selectRecordInfo.orderSpecificationRequire.length > 0
            "
          >
            <div class="label" style="width: 98px">商品规格要求：</div>
            <div class="content">
              <div style="line-height: 20px">
                {{ selectRecordInfo.orderSpecificationRequire }}
              </div>
            </div>
          </div>
        </el-row>
        <el-row>
          <div
            class="flex-start col-24"
            v-if="selectRecordInfo?.particularEmphasis && selectRecordInfo.particularEmphasis.length > 0"
          >
            <div class="label" style="text-align: start">特别强调：</div>
            <div class="content template-pre">
              <template v-if="selectRecordInfo.particularEmphasis != null">
                {{ handleR(selectRecordInfo?.particularEmphasis) }}
              </template>
              <div v-else>-</div>
            </div>
          </div>
        </el-row>
        <el-row>
          <div
            style="overflow: hidden; display: flex; align-items: start"
            v-if="selectRecordInfo.particularEmphasisPic && selectRecordInfo.particularEmphasisPic.length > 0"
          >
            <div
              class="label"
              style="text-align: start; width: 90px"
              v-if="!selectRecordInfo?.particularEmphasis || selectRecordInfo.particularEmphasis == ''"
            >
              特别强调：
            </div>
            <ViewerImageList
              :data="selectRecordInfo?.particularEmphasisPic"
              is-preview-all
              :show-delete-btn="false"
            />
          </div>
        </el-row>
        <!-- <el-row>
          <div class="flex-start col-24">
            <div class="label">匹配模特注意事项：</div>
            <div class="content more-ell">
            </div>
          </div>
        </el-row> -->
      </div>
      <div v-else class="flex-center" style="margin-top: 18%">此次无修改内容</div>
    </div>
    <div v-else class="history-box empty flex-center">暂无变更记录</div>
  </el-dialog>
</template>

<script setup>
import ViewerImageList from '@/components/ImagePreview/viewerImageList.vue'
import { getVideoHistoryChangeRecord } from '@/api/order/order'
import { picCountOptions, videoFormatOptions, videoStyleList } from '@/views/order/list/data.js'

const dialogVisible = ref(false)
const activeTab = ref(0)
const loading = ref(false)
defineExpose({ open })

const historyRecordList = ref([])
const selectRecordInfo = ref({
  changeUserName: '',
  changeUserId: '',
  changeTime: '',
  platform: '',
  shootingCountry: '',
  modelType: '',
  videoFormat: '',
  intentionModelName: '',
  intentionModelId: '',
  videoDuration: '',
  videoStyle: '',
  productChinese: '',
  productEnglish: '',
  productPic: '',
  productLink: '',
  referenceVideoLink: '',
  sellingPointProduct: '',
  referencePic: [],
  clipsRequired: [],
  shootRequired: [],
  cautions: [],
  logType: '',
  productCategoryName: '',
  particularEmphasis: '',
  orderSpecificationRequire: '',
  cautionsPics: [],
  particularEmphasisPic: [],
  rollbackId: '',
  isNothing: false,
  isGund: '',
})

const logTypeList = [
  { label: '初始记录', value: 1 },
  { label: '商家同意后修改', value: 3 },
  { label: '待匹配订单时修改', value: 2 },
  { label: '审核订单时修改', value: 4 },
  { label: '待发货时修改', value: 5 },
  { label: '待完成时修改', value: 6 },
]

function open(id) {
  activeTab.value = ''
  getHistoryRecord(id)
  dialogVisible.value = true
}
function handleClose() {
  activeTab.value = 0
  initSelectRecordInfo()
  dialogVisible.value = false
}

function getHistoryRecord(id) {
  loading.value = true
  getVideoHistoryChangeRecord(id)
    .then(res => {
      historyRecordList.value = res.data || []
      if (
        historyRecordList.value &&
        historyRecordList.value.length > 0 &&
        historyRecordList.value[0].changeLogInfoList &&
        historyRecordList.value[0].changeLogInfoList.length > 0
      ) {
        handleData(historyRecordList.value[0].changeLogInfoList)
      }
      // console.log(selectRecordInfo.value)

      selectRecordInfo.value.changeUserName = historyRecordList.value[0].changeUser?.name
      selectRecordInfo.value.changeUserId = historyRecordList.value[0].changeUser?.id
      selectRecordInfo.value.changeTime = historyRecordList.value[0].changeTime
      // console.log(historyRecordList.value[0], 666)
      selectRecordInfo.value.logType = historyRecordList.value[0].logType
    })
    .finally(() => {
      loading.value = false
      activeTab.value = 0
    })
}
function handleR(data) {
  if (data && data.length > 0) {
    return data.replace(/\r/g, '\r\n') || ''
  } else return ''
}

function handleData(list) {
  for (let key in selectRecordInfo.value) {
    list.forEach(item => {
      item.fieldName == key ? (selectRecordInfo.value[key] = item.value) : ''
    })
  }
}

function close() {
  dialogVisible.value = false
}

function initSelectRecordInfo() {
  selectRecordInfo.value = {
    platform: '',
    modelType: '',
    changeTime: '',
    videoFormat: '',
    intentionModelName: '',
    intentionModelId: '',
    videoDuration: '',
    videoStyle: '',
    productChinese: '',
    shootingCountry: '',
    productEnglish: '',
    productLink: '',
    productPic: '',
    referenceVideoLink: '',
    sellingPointProduct: '',
    clipsRequired: [],
    shootRequired: [],
    cautions: [],
    referencePic: [],
    logType: '',
    productCategoryName: '',
    particularEmphasisPic: [],
    particularEmphasis: '',
    orderSpecificationRequire: '',
    cautionsPics: [],
    rollbackId: '',
    isNothing: false,
    isGund: '',
  }
}

function handleClick(index) {
  initSelectRecordInfo()
  activeTab.value = index
  selectRecordInfo.value.changeUserName = historyRecordList.value[index].changeUser?.name
  selectRecordInfo.value.changeUserId = historyRecordList.value[index].changeUser?.id
  selectRecordInfo.value.changeTime = historyRecordList.value[index].changeTime
  selectRecordInfo.value.logType = historyRecordList.value[index].logType
  if (
    historyRecordList.value[index].changeLogInfoList &&
    historyRecordList.value[index].changeLogInfoList.length > 0
  ) {
    handleData(historyRecordList.value[index].changeLogInfoList)
  }
  // selectRecordInfo.value = historyRecordList.value[index]
}
</script>

<style lang="scss" scoped>
.history-box {
  min-height: 500px;
  :deep(.el-steps--simple) {
    padding: 13px;
    overflow: auto;
    .el-step.is-simple {
      flex-grow: 0;
      flex-shrink: 0;
      flex-basis: auto !important;
      width: 200px;
    }
    .el-step.is-simple.is-flex {
      max-width: 100% !important;
    }
    .el-step__arrow {
      transform: rotateY(180deg);
    }
    .el-step__title {
      max-width: 100%;
    }
    .el-step__icon.is-icon {
      display: none;
    }
    .el-step.is-simple.is-flex {
      max-width: 100%;
    }
    .el-step__title.is-process {
      text-align: center;
      color: var(--el-color-primary);
    }
    .el-step__title.is-finish,
    .is-wait {
      text-align: center;
      color: #333;
    }
  }
  .edit-head {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin: 10px 0;
    &__title {
      font-size: 18px;
      font-weight: 700;
      color: #333;
    }
    &__content {
      font-size: 16px;
    }
  }
  .edit-content {
    margin: 10px 0;
    .el-col-8,
    .el-col-12,
    .el-col-16 {
      display: flex;
      margin-bottom: 20px;

      .label {
        flex-shrink: 0;
        width: 70px;
        text-align: end;
      }
      .content {
        flex-shrink: 0;
        width: calc(100% - 95px);
        word-break: break-all;
        white-space: pre-line;
      }
    }
    .col-24 {
      align-items: flex-start;
      margin-bottom: 20px;
      width: 100%;

      .label {
        flex-shrink: 0;
        width: 70px;
        text-align: end;
      }
      .content {
        flex-shrink: 0;
        width: calc(100% - 120px);
        word-break: break-all;
        white-space: pre-line;
      }
      .tip {
        flex-shrink: 0;
        color: #7f7f7f;
      }
    }
  }
  .empty {
    height: 250px;
    font-size: 18px;
    color: #7f7f7f;
  }
}
</style>
