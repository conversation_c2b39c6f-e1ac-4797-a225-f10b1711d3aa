<template>
  <div>
    <el-drawer
      size="60%"
      v-model="drawer"
      title="I am the title"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      style="background: #ecf4fd"
      @close="close"
    >
      <template #header>
        <div class="flex-between">
          <div class="flex-center">
            <span style="font-weight: 800; color: #333; margin-right: 10px">
              {{ dialogType == 'edit' ? '修改' : '审核' }}订单
            </span>
            <span style="margin: 0 10px; font-size: 14px">视频编码：{{ form.videoCode || '' }}</span>
            <span style="font-size: 14px">订单运营：{{ form.createOrderUserName || '' }}</span>
            <span style="font-size: 14px">&nbsp;/&nbsp;{{ form.createOrderUserNickName || '' }}</span>
          </div>
          <div>
            <el-button
              round
              v-btn
              :loading="nextLoading"
              v-if="prevVideoId"
              @click="getDetails(prevVideoId, 'next')"
            >
              上一单
            </el-button>
            <el-button
              round
              v-btn
              :loading="nextLoading"
              v-if="nextVideoId"
              @click="getDetails(nextVideoId, 'next')"
            >
              下一单
            </el-button>
          </div>
        </div>
      </template>
      <el-form
        label-width="100px"
        :model="form"
        ref="formRef"
        :rules="rules"
        v-loading="loading"
        label-position="left"
      >
        <div class="drawer-box">
          <div class="box-title flex-between">
            <div>订单信息</div>
            <div class="flex-center" style="gap: 0 10px">
              <div class="box-title-right" v-if="handleShowDiscount(form.orderDiscountDetailVOS, '4')">
                每月首单立减：
                <span style="color: #d9001b">-￥{{ handleDiscount(form.orderDiscountDetailVOS, '4') }}</span>
              </div>
              <div class="box-title-right" v-if="handleShowDiscount(form.orderDiscountDetailVOS, '1')">
                限时满减优惠：
                <span style="color: #d9001b">-￥{{ handleDiscount(form.orderDiscountDetailVOS, '1') }}</span>
              </div>
            </div>
          </div>
          <el-divider style="margin: 8px 0" />
          <div style="display: flex">
            <div class="fs-0 img-box">
              <template v-if="form.productPic">
                <el-image
                  :src="$picUrl + form.productPic + '!squarecompress'"
                  class="flex-center"
                  style="width: 103px; height: 103px"
                  @click="showViewer([form.productPic])"
                >
                  <template #error>
                    <img
                      :src="$picUrl + 'static/assets/no-img.png'"
                      alt=""
                      style="width: 100%; height: 100%"
                    />
                  </template>
                </el-image>
              </template>
              <template v-else>
                <el-image
                  :src="$picUrl + productLinkImg + '!squarecompress'"
                  class="flex-center"
                  style="width: 103px; height: 103px"
                  @click="showViewer([productLinkImg])"
                >
                  <template #error>
                    <img
                      :src="$picUrl + 'static/assets/no-img.png'"
                      alt=""
                      style="width: 100%; height: 100%"
                    />
                  </template>
                </el-image>
              </template>
              <el-button
                style="margin-top: 10px"
                link
                type="primary"
                v-btn
                :disabled="disabled_edit_status"
                @click="handleDragUploadDialogOpen('product')"
              >
                重新上传
              </el-button>
              <!-- <div class="img-box-tip" style=""  ></div> -->
            </div>
            <div style="flex: 1">
              <el-row>
                <el-col :span="12">
                  <!-- <el-form-item label="产品名称" prop="productEnglish">
                    <SpanOrInput
                      :filedName="'productEnglish'"
                      :value="form.productEnglish"
                      @update:value="handleUpdate"
                      :maxlength="50"
                    />
                  </el-form-item> -->
                  <div class="label">产品名称</div>
                  <div class="content">
                    <SpanOrInput
                      :filedName="'productEnglish'"
                      :value="form.productEnglish"
                      @update:value="handleUpdate"
                      :maxlength="225"
                      :disabled="disabled_edit_status"
                    />
                  </div>
                </el-col>
                <el-col :span="12">
                  <!-- <el-form-item label="中文名称" prop="productChinese">
                    <SpanOrInput
                      :filedName="'productChinese'"
                      :value="form.productChinese"
                      @update:value="handleUpdate"
                      :maxlength="30"
                    />
                  </el-form-item> -->
                  <div class="label">中文名称</div>
                  <div class="content">
                    <SpanOrInput
                      :filedName="'productChinese'"
                      :value="form.productChinese"
                      @update:value="handleUpdate"
                      :maxlength="225"
                      :disabled="disabled_edit_status"
                    />
                  </div>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="24">
                  <!-- <el-form-item label="产品链接">
                    <SpanOrInput
                      :filedName="'productLink'"
                      :value="form.productLink"
                      @update:value="handleUpdate"
                      :isLink="true"
                    />
                  </el-form-item> -->
                  <div class="label">产品链接</div>
                  <div class="content">
                    <SpanOrInput
                      :filedName="'productLink'"
                      :value="form.productLink"
                      @update:value="handleUpdate"
                      :isLink="true"
                      :disabled="disabled_edit_status"
                    />
                  </div>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="12">
                  <div class="label">使用平台</div>
                  <div class="content">
                    <!-- <SpanOrRadio
                      v-model="form.platform"
                      :filedName="'platform'"
                      :list="biz_model_platform"
                      :isDisabled="form.picCount != null"
                      @changeValue="changeFormValue"
                    /> -->
                    <SpanOrSelect
                      v-model="form.platform"
                      :filedName="'platform'"
                      :list="biz_model_platform"
                      :disabled="disabled_edit_status"
                      :isDisabled="form.picCount != null"
                      @changeValue="changeFormValue"
                    />
                  </div>
                </el-col>
                <el-col :span="12">
                  <div class="label">拍摄国家</div>
                  <div class="content">
                    <SpanOrSelect
                      v-model="form.shootingCountry"
                      :filedName="'shootingCountry'"
                      :list="biz_nation"
                      :disabled="disabled_edit_status"
                      @changeValue="changeFormValue"
                    />
                    <!-- <SpanOrRadio
                      v-model="form.shootingCountry"
                      :filedName="'shootingCountry'"
                      :list="biz_nation"
                      @changeValue="changeFormValue"
                    /> -->
                  </div>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="12">
                  <div class="label">模特类型</div>
                  <div class="content">
                    <!-- <SpanOrSelect
                      v-if="isMultiSelModel"
                      v-model="form.modelTypeList"
                      :filedName="'modelTypeList'"
                      :multiple="true"
                      :list="biz_model_type"
                      @changeValue="changeModelValue"
                    /> -->
                    <SpanOrSelect
                      v-model="form.modelType"
                      :filedName="'modelType'"
                      :list="biz_model_type"
                      :disabled="disabled_edit_status"
                      :isDisabled="modelTypeDisabled"
                      @changeValue="changeFormValue"
                    />
                    <!-- <SpanOrCheck
                      v-if="isMultiSelModel"
                      v-model="form.modelTypeList"
                      :filedName="'modelTypeList'"
                      :list="biz_model_type"
                      @changeValue="changeModelValue"
                    />
                    <SpanOrRadio
                      v-else
                      v-model="form.modelType"
                      :filedName="'modelType'"
                      :list="biz_model_type"
                      :isDisabled="modelTypeDisabled"
                      @changeValue="changeFormValue"
                    /> -->
                  </div>
                </el-col>
                <el-col :span="12">
                  <div class="label">视频格式</div>
                  <div class="content">
                    <SpanOrSelect
                      v-model="form.videoFormat"
                      :filedName="'videoFormat'"
                      :list="videoFormatList"
                      :disabled="disabled_edit_status"
                      :isDisabled="platformEmun.Tiktok == form.platform"
                    />
                    <!-- <SpanOrRadio
                      v-model="form.videoFormat"
                      :filedName="'videoFormat'"
                      :list="videoFormatList"
                      :isDisabled="platformEmun.Tiktok == form.platform"
                    /> -->
                  </div>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="12">
                  <div class="label">照片数量</div>
                  <div class="content">
                    {{
                      picCountOptionsList.find(item => item.value == form.picCount)?.label.slice(0, 2) || '无'
                    }}
                  </div>
                </el-col>
                <el-col :span="12" style="align-items: flex-start" v-if="form.picCount">
                  <div class="label">参考图片</div>
                  <div class="content" style="display: flex">
                    <template v-if="isShowUpload">
                      <div class="image-list" v-for="(item, i) in form.referencePic" :key="item.id">
                        <div class="image-modal flex-around">
                          <el-icon size="20" color="#ffffffc4">
                            <Delete style="cursor: pointer" @click="doDeletePicUrl(i)" />
                          </el-icon>
                          <el-icon size="20" color="#ffffffc4">
                            <View style="cursor: pointer" @click="doShowPicUrl(i)" />
                          </el-icon>
                        </div>
                        <el-image
                          ref="imgViewRef"
                          preview-teleported
                          :src="$picUrl + item.picUrl + '!squarecompress'"
                          fit="fill"
                          class="image-item"
                          :preview-src-list="item.picUrl ? [item.picUrl] : []"
                        ></el-image>
                      </div>
                      <div
                        class="image-upload"
                        @click="form.picCount ? DragUploadDialogRef.open() : ''"
                        v-if="!disabled_edit && form.referencePic && form.referencePic.length < addPicNumber"
                      >
                        <el-icon size="28" color="#909399"><Plus /></el-icon>
                      </div>
                    </template>
                    <el-button
                      link
                      type="primary"
                      v-btn
                      :disabled="disabled_edit_status"
                      @click="handleShowUpload"
                    >
                      {{ isShowUpload ? '完成' : '编辑' }}
                    </el-button>
                    <el-button
                      link
                      type="primary"
                      v-if="form.referencePic && form.referencePic.length > 0 && !isShowUpload"
                      v-btn
                      @click="doShowPicUrl"
                    >
                      查看
                    </el-button>
                  </div>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="12">
                  <div class="label">意向模特</div>
                  <div class="content" style="display: flex; align-items: center">
                    <div v-if="form.selModelInfo?.account" style="margin-right: 10px">
                      <span style="color: #4c8ab8" @click="showModelInfoDialog(form.selModelInfo.id)">
                        {{ form.selModelInfo.name }}
                      </span>
                    </div>
                    <el-button
                      link
                      type="primary"
                      v-btn
                      :disabled="disabled_edit_status"
                      @click="changeSelModel"
                    >
                      <span v-if="form.selModelInfo?.account">变更</span>
                      <span v-else>选择</span>
                    </el-button>
                    <el-button
                      v-if="form.selModelInfo?.account && !disabled_edit_status"
                      link
                      type="danger"
                      v-btn
                      @click="removeSelModel"
                    >
                      <span>删除</span>
                    </el-button>
                  </div>
                </el-col>
                <el-col :span="12">
                  <div class="label">参考视频</div>
                  <div class="content">
                    <SpanOrInput
                      ref="referenceVideoLinkRef"
                      :filedName="'referenceVideoLink'"
                      :value="form.referenceVideoLink"
                      @update:value="handleUpdate"
                      :isLink="true"
                    />
                  </div>
                </el-col>
              </el-row>

              <el-row>
                <el-col :span="24">
                  <el-form-item label="产品卖点" prop="sellingPointProduct">
                    <SpanOrText
                      :filedName="'sellingPointProduct'"
                      :value="form.sellingPointProduct"
                      :maxlength="8000"
                      :placeholder="'请用英文输入产品卖点'"
                      @update:value="handleUpdate"
                    />
                  </el-form-item>
                  <!-- <div class="label">拍摄建议</div> -->
                  <!-- <div class="content">
                    <SpanOrText
                      :filedName="'sellingPointProduct'"
                      :value="form.sellingPointProduct"
                      :maxlength="5000"
                      :placeholder="'请用英文输入产品卖点'"
                      @update:value="handleUpdate"
                    />
                  </div> -->
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="24">
                  <div class="label">卖点翻译</div>
                  <div class="content" style="display: flex; cursor: auto">
                    <div
                      style="--l: 1; flex: 1; line-break: anywhere; white-space: pre-wrap"
                      :class="{ 'more-ell': !showAllSussgestions }"
                    >
                      {{ shootingTransLateSuggestion || '-' }}
                    </div>
                    <div>
                      <el-button
                        link
                        v-btn
                        type="primary"
                        v-if="shootingTransLateSuggestion != ''"
                        style="margin-left: 10px"
                        @click="showAllSussgestions = !showAllSussgestions"
                      >
                        {{ showAllSussgestions ? '收起' : '展开' }}
                      </el-button>
                    </div>
                  </div>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="24">
                  <el-form-item label="拍摄建议" prop="demands">
                    <SpanOrText
                      :filedName="'demands'"
                      :value="form.demands"
                      :maxlength="8000"
                      :placeholder="'请用英文输入拍摄建议'"
                      @update:value="handleUpdate"
                    />
                  </el-form-item>
                  <!-- <div class="label">产品卖点</div>
                  <div class="content">
                    <SpanOrText
                      :filedName="'demands'"
                      :value="form.demands"
                      :maxlength="5000"
                      :placeholder="'请用英文输入产品卖点'"
                      @update:value="handleUpdate"
                    />
                  </div> -->
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="24">
                  <div class="label">建议翻译</div>
                  <div class="content" style="display: flex; cursor: auto">
                    <div
                      style="--l: 1; flex: 1; line-break: anywhere; white-space: pre-wrap"
                      :class="{ 'more-ell': !showAllDemands }"
                    >
                      {{ demansTransLate || '-' }}
                    </div>
                    <div>
                      <el-button
                        link
                        v-btn
                        type="primary"
                        v-if="demansTransLate != ''"
                        style="margin-left: 10px"
                        @click="showAllDemands = !showAllDemands"
                      >
                        {{ showAllDemands ? '收起' : '展开' }}
                      </el-button>
                    </div>
                  </div>
                </el-col>
              </el-row>
            </div>
          </div>
        </div>
        <div class="drawer-box" style="margin-top: 20px">
          <div class="box-title">补充信息</div>
          <el-divider style="margin: 8px 0 12px 0" />
          <div class="flex-start" style="align-items: flex-start">
            <el-form-item label="产品品类" prop="productCategory" label-width="110">
              <el-select
                v-model="form.productCategory"
                :disabled="disabled_edit_status"
                multiple
                collapse-tags
                clearable
                placeholder="请选择"
              >
                <el-option
                  v-for="item in productCategoryList"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="视频时长" prop="videoDuration" label-width="100" style="margin-left: 30px">
              <el-radio-group v-model="form.videoDuration" :disabled="disabled_edit_status">
                <el-radio-button :value="120">120S</el-radio-button>
                <el-radio-button :value="90">90S</el-radio-button>
                <el-radio-button :value="1">自定义</el-radio-button>
              </el-radio-group>
              <el-input-number
                title=""
                v-if="form.videoDuration === 1"
                v-model="form.videoDurationCustom"
                :disabled="disabled_edit_status"
                :min="1"
                :max="99999"
                :precision="0"
                :step="1"
                controls-position="right"
                style="margin: -10px 0 0 10px"
                placeholder="请输入视频需要的时长(单位:秒)"
                clearable
              />
            </el-form-item>
            <el-form-item label="通品" prop="isGund" label-width="60" style="margin-left: 20px">
              <el-radio-group v-model="form.isGund">
                <el-radio-button :value="1">是</el-radio-button>
                <el-radio-button :value="0">否</el-radio-button>
              </el-radio-group>
            </el-form-item>
          </div>
          <el-form-item label="视频风格" prop="videoStyle" label-width="110">
            <el-radio-group v-model="form.videoStyle" :disabled="disabled_edit_status">
              <el-radio-button v-for="item in videoStyleList" :key="item.value" :value="item.value">
                {{ item.label }}
              </el-radio-button>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="模特要求" prop="cautionsTemp" label-width="110">
            <el-input
              maxlength="800"
              v-model="form.cautionsTemp"
              placeholder="对模特类型的要求，如性别、长相、身材（如只能要穿S码的模特）；对模特有的东西的要求，如要有吉他、有电钻"
              clearable
            />
          </el-form-item>
          <el-form-item label="商品规格要求" prop="orderSpecificationRequire" label-width="110">
            <el-input
              maxlength="300"
              v-model="form.orderSpecificationRequire"
              placeholder="填写需要模特提供的发货信息，如需要提供三围、提供手机型号、提供地毯尺寸等"
              clearable
            />
          </el-form-item>
          <el-form-item label="特别强调" prop="particularEmphasis" label-width="110">
            <el-input
              type="textarea"
              maxlength="500"
              v-model="form.particularEmphasis"
              placeholder="填写需要特别跟模特强调的拍摄内容，如 开箱+讲解+展示"
              clearable
              :autosize="{ minRows: 3, maxRows: 6 }"
            />
          </el-form-item>
          <el-form-item label-width="110">
            <DragImageUpload
              v-model="emphasisImageList"
              @openUploadDialog="handleDragUploadDialogOpen('particularEmphasisPic')"
              :limit="10"
            />
            <div class="prompt-text" style="width: 100%">
              <div style="line-height: 20px; font-size: 12px; color: #7f7f7f">
                1.请上传图片大小不超过5M，格式为 png/jpg、jpeg 的文件;
                <br />
                2.最多可以传10张
              </div>
            </div>
          </el-form-item>
          <!-- <el-form-item label="拍摄要求" prop="cautionsTemp" label-width="110">
            <div>
              <div class="group-box" style="margin-top: 0">
                <div style="margin: 0 10px 10px 0" class="fs-0">基础要求</div>
                <el-checkbox-group v-model="shootRequired">
                  <el-checkbox-button v-for="item in shootRequiredList" :key="item.value" :value="item.value">
                    {{ item.cn }}
                  </el-checkbox-button>
                </el-checkbox-group>
              </div>
              <div class="group-box">
                <div style="margin: 0 10px 10px 0" class="fs-0">场景&emsp;&emsp;</div>
                <el-checkbox-group v-model="shootRequired">
                  <el-checkbox-button v-for="item in shootRequiredList" :key="item.value" :value="item.value">
                    {{ item.cn }}
                  </el-checkbox-button>
                  <el-button v-btn @click="handleDefined('shoot')">自定义</el-button>
                </el-checkbox-group>
              </div>
              <div class="group-box">
                <div style="margin: 0 10px 10px 0" class="fs-0">
                  <div>拍摄内容</div>
                </div>
                <el-checkbox-group v-model="shootRequired">
                  <el-checkbox-button v-for="item in shootRequiredList" :key="item.value" :value="item.value">
                    {{ item.cn }}
                  </el-checkbox-button>
                  <el-button v-btn @click="handleDefined('shoot')">自定义</el-button>
                </el-checkbox-group>
              </div>
            </div>
          </el-form-item> -->
          <el-form-item label="剪辑要求" prop="clipsRequiredTemp" label-width="110">
            <el-input
              maxlength="800"
              v-model="form.clipsRequiredTemp"
              placeholder="填写剪辑时需注意的事项"
              clearable
            />
          </el-form-item>
        </div>
      </el-form>
      <template #footer>
        <div class="flex-center">
          <el-button
            style="padding: 20px 63px"
            v-if="dialogType === 'audit'"
            v-btn
            @click="saveCacheParams"
            :disabled="isShowAudit"
          >
            保存
          </el-button>
          <el-button
            type="primary"
            style="padding: 20px 49px; margin-left: 150px"
            v-if="dialogType === 'audit'"
            v-btn
            @click="doSubmit"
            :disabled="isShowAudit"
          >
            审核通过
          </el-button>
          <el-button v-if="dialogType === 'edit'" size="large" type="primary" v-btn round @click="saveEdit">
            确认修改
          </el-button>
        </div>
      </template>
    </el-drawer>

    <el-dialog
      align-center
      append-to-body
      title="自定义"
      v-model="userDefineDialog"
      width="600px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="closeUserDefineDialog"
    >
      <el-form label-width="100px" :model="userDefineForm" :rules="userDefineRules" ref="userDefineRef">
        <el-form-item label="字段名称" prop="cnName">
          <el-input
            maxlength="10"
            v-model="userDefineForm.cnName"
            placeholder="填写自定义的名称字段"
            clearable
          />
        </el-form-item>
        <el-form-item label="字段英文名" prop="enName">
          <el-input
            maxlength="50"
            v-model="userDefineForm.enName"
            placeholder="英文名将会展示给模特，请确认翻译无误后填写"
            clearable
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="closeUserDefineDialog">取消</el-button>
        <el-button type="primary" @click="handleUserDefineForm">保存</el-button>
      </template>
    </el-dialog>

    <DragUploadDialog
      ref="DragUploadDialogRef"
      title="上传参考图片"
      :limit="addPicNumber1"
      @success="upSuccess"
      bucketType="order"
    />

    <DragUploadDialog
      ref="DragUploadDialogRef1"
      :title="DragUploadDialogTitle"
      :limit="dragUploadLimit"
      @success="changePicSuccess"
      bucketType="order"
    />

    <SelectSingleModel
      ref="SelectSingleModelRef"
      :nation="form.shootingCountry + ''"
      :modelType="form.modelType + ''"
      :platform="form.platform + ''"
      @selectModelInfo="handleSelectModelInfo"
    />
    <ModelInfoDialog ref="ModelInfoDialogRef" />
  </div>
</template>

<script setup>
import DragImageUpload from '@/components/ImageUpload/dragImageUpload.vue'
import ModelInfoDialog from '@/views/model/ModelInfoDialog.vue'
import { modelCategorySelectRank } from '@/api/model/model'
import { getVideoOrderDetail, getTranslate, getOneTranslate, editVideoOrder } from '@/api/order/order'
import { editMatchVideoOrder } from '@/api/order/preselection'
import { chinese_reg, englishCharacter_d_reg } from '@/utils/RegExp'
import { ElMessage, ElMessageBox } from 'element-plus'
import CropperDialog from '@/components/Cropper/cropperDialog'
import SelectSingleModel from '@/views/order/components/dialog/selectSingleModel.vue'

import InputText from '@/components/InputText/index.vue'
import SpanOrInput from '@/views/order/components/spanOrInput.vue'
import SpanOrRadio from '@/views/order/components/spanOrRadio.vue'
import SpanOrSelect from '@/views/order/components/spanOrSelect.vue'
import SpanOrCheck from '@/views/order/components/spanOrCheck.vue'
import SpanOrText from '@/views/order/components/spanOrText.vue'
import {
  orderStatusMap,
  videoFormatOptions,
  picCountOptions,
  Platform,
  videoStyleList,
} from '@/views/order/list/data.js'
import { useViewer } from '@/hooks/useViewer'

const { showViewer } = useViewer()
const { proxy } = getCurrentInstance()
const { biz_model_platform, biz_nation, biz_model_type } = proxy.useDict(
  'biz_model_platform',
  'biz_nation',
  'biz_model_type'
)

// const isEditInfo = ref(false)
// const isWatching = ref(true)
// const stopHandle = watchEffect(() => {
//       if (isWatching.value) {
//         // 修改外部状态
//         isEditInfo.value = true;
//         stopWatching()
//       }
//     });

// // 停止监听的方法
// const stopWatching = () => {
//   isWatching.value = false
//   stopHandle() // 取消监听
// }

//mock数据
const shootRequired = ref([])
const shootRequiredList = ref([
  {
    cn: '4k',
    en: '4k',
    value: '1',
  },
  {
    cn: '1080p',
    en: '1080p',
    value: '2',
  },
  {
    cn: '720p',
    en: '720p',
    value: '3',
  },
  {
    cn: '2K',
    en: '2K',
    value: '4',
  },
  {
    cn: '3K',
    en: '3K',
    value: '5',
  },
])

const drawer = ref(false)

const pathHead = proxy.$picUrl
const videoFormatList = ref(videoFormatOptions)
const picCountOptionsList = ref(picCountOptions)
const platformEmun = ref(Platform)
const disabled_edit = ref(false)
const addPicNumber = ref(2)
const addPicNumber1 = ref(2)
const disabled = ref(false)

const translateLoading = ref(false)
const isShowEditShootRequired = ref(false)
const isShowClipsErrow = ref(false)
const productCategoryList = ref([])
const CropperDialogRef = ref(null)
const imgFile = ref({})
const SelectSingleModelRef = ref(null)
const DragUploadDialogRef1 = ref(null)
const DragUploadDialogRef = ref(null)
const DragUploadDialogTitle = ref('')
const dragUploadLimit = ref(1)

const orderStatus = ref('')
const orderId = ref('')
const dialogType = ref('audit')
const createUserId = ref('')
const loading = ref(false)
const formRef = ref()
const imageList = ref([])
const emphasisImageList = ref([])

const referenceVideoLinkRef = ref()

const emits = defineEmits(['success'])

// 修改订单-需发货/待完成状态不可编辑
const disabled_edit_status = computed(() => {
  if (dialogType.value === 'edit') {
    if (orderStatus.value === orderStatusMap['需发货'] || orderStatus.value === orderStatusMap['待完成']) {
      return true
    }
  }
  return false
})

const form = ref({
  videoCode: '',
  platform: '0',
  videoFormat: '1',
  shootingCountry: '7',
  modelType: '1',
  modelTypeList: [1],
  productChinese: '',
  productEnglish: '',
  isLink: 1,
  productLink: '',
  referenceVideoLink: '',
  picCount: 1,
  productPic: [],
  referencePic: [],
  productPicInfo: {},
  filePicList: [],
  videoDuration: 120,
  videoDurationCustom: 120,
  // customVideoDuration: 0,
  productCategory: '',
  selModelInfo: {},
  demands: '',
  shootRequired: [],
  clipsRequired: [],
  clipsRequiredTemp: '',
  cautionsTemp: '',
  cautionsPics: [],
  particularEmphasisPic: [],
  cautions: [],
  sellingPointProduct: '',
  particularEmphasis: '',
  orderSpecificationRequire: '',
  videoStyle: 0,
  isGund: 1,
  createOrderUserName: '',
  orderDiscountDetailVOS: [],
})
const savedUnwatch = ref(null)
const isEditHint = ref(false)
const watchFormChanges = () => {
  if (savedUnwatch.value) {
    savedUnwatch.value()
  }
  savedUnwatch.value = watch(
    () => [
      form.value.productPic,
      form.value.productEnglish,
      form.value.productChinese,
      form.value.productLink,
      form.value.platform,
      form.value.shootingCountry,
      form.value.modelTypeList,
      form.value.modelType,
      form.value.videoFormat,
      form.value.referencePic,
      form.value.selModelInfo,
      form.value.referenceVideoLink,
      form.value.demands,
      form.value.sellingPointProduct,
    ],
    (newVal, oldVal) => {
      isEditHint.value = true
      savedUnwatch.value()
    }
  )
}
//平台切换时临时数据
const temporaryChangeData = ref({
  platform: '0',
  shootingCountry: '7',
  modelType: '1',
  modelTypeList: ['1'],
})

const productLinkImg = ref('')
const prevVideoId = ref('')
const nextVideoId = ref('')
const isShowAudit = ref(false)
const showCheckbox = ref(false)
const showAllDemands = ref(false)
const showAllSussgestions = ref(false)
const handleUpdate = (newValue, key) => {
  form.value[key] = newValue
  // if (key == 'referenceVideoLink' && newValue != '') {
  //   form.value.sellingPointProduct = ''
  // }
  // if (key == 'sellingPointProduct' && newValue != '') {
  //   form.value.referenceVideoLink = ''
  // }
  if (newValue != '') {
    switch (key) {
      case 'referenceVideoLink':
        // form.value.sellingPointProduct = ''
        // shootingTransLateSuggestion.value = ''
        break
      case 'sellingPointProduct':
        doTranslate(2)
        break
      case 'demands':
        doTranslate(1)
      default:
        break
    }
  } else {
    if (key == 'sellingPointProduct') {
      shootingTransLateSuggestion.value = ''
    } else if (key == 'demands') {
      demansTransLate.value = ''
    }
  }
}

// doTranslate()

const rules = ref({
  platform: [{ required: true, message: '请选择平台', trigger: 'blur' }],
  videoFormat: [{ required: true, message: '请选择平台视频格式', trigger: 'blur' }],
  shootingCountry: [{ required: true, message: '请选择拍摄国家', trigger: 'blur' }],
  modelTypeList: [{ required: true, message: '请选择模特类型', trigger: 'change' }],
  productChinese: [
    { required: true, message: '请输入产品中文名', trigger: 'blur' },
    // { pattern: chineseCharacter_d_reg, message: '请输入产品中文名', trigger: 'change' },
  ],
  productEnglish: [
    { required: true, message: '请输入产品英文名', trigger: 'blur' },
    { pattern: englishCharacter_d_reg, message: '请输入产品英文名', trigger: 'change' },
  ],
  isLink: [{ required: true, validator: checkIsLink, trigger: 'change' }],
  productPicInfo: [{ required: true, validator: selectProductPic, trigger: 'blur' }],
  // clipsRequiredTemp: [{ required: false, validator: checkClipsRequired, trigger: 'change' }],
  demands: [{ required: false, validator: checkDemands, trigger: 'change' }],
  productCategory: [{ required: true, message: '请选择产品品类', trigger: 'change' }],
  videoDuration: [{ required: true, message: '请选择视频时长', trigger: 'change' }],
  sellingPointProduct: [{ required: false, validator: checksellingPointProduct, trigger: 'change' }],
  videoStyle: [{ required: true, message: '请选择视频风格', trigger: 'change' }],
  isGund: [{ required: true, message: '请选择是/否', trigger: 'change' }],
})

function checkIsLink(rule, value, callback) {
  if (form.value.isLink == 0) return callback()
  if ((value && !form.value.productLink) || !form.value.productLink.startsWith('https://')) {
    return callback(new Error('请输入真实的产品链接'))
  }
  return callback()
}

function selectProductPic(rule, value, callback) {
  if (!form.value.filePicList || (form.value.filePicList && form.value.filePicList.length === 0)) {
    return callback(new Error('请上传产品图'))
  }
  return callback()
}

function checksellingPointProduct(rule, value, callback) {
  if (value && value != '') {
    if (chinese_reg.test(value)) {
      return callback(new Error('*请用英文，简短描述重要的产品卖点'))
    }
  }
  return callback()
}

function checkDemands(rule, value, callback) {
  if (value && value != '') {
    if (chinese_reg.test(value)) {
      return callback(new Error('*请用英文，简短描述重要的拍摄建议'))
    }
  }
  return callback()
}

const shootingTransLateSuggestion = ref('')
const demansTransLate = ref('')
//翻译
function doTranslate(type) {
  if (isShowTranslateContent.value) {
    isShowTranslateContent.value = false
    return
  }
  let dataStr = ''
  if (type == 2) {
    dataStr = form.value.sellingPointProduct
  }
  if (type == 1) {
    dataStr = form.value.demands
  }
  getOneTranslate({ language: 1, originText: dataStr })
    .then(res => {
      let data = res.data.targetText
      if (data) data = data.replace(/\r/g, '\r\n')
      if (type == 2) {
        shootingTransLateSuggestion.value = data
      }
      if (type == 1) {
        demansTransLate.value = data
      }
      translateLoading.value = false
    })
    .finally(() => (translateLoading.value = false))
}

const open = (id, type, cId) => {
  orderId.value = id
  dialogType.value = type
  createUserId.value = cId
  if (type == 'audit') {
    showAllDemands.value = true
    showAllSussgestions.value = true
  } else {
    showAllDemands.value = false
    showAllSussgestions.value = false
  }
  drawer.value = true
  getCategoryList()
  getDetails(id, type)
}
function close() {
  drawer.value = false
  if (savedUnwatch.value) {
    savedUnwatch.value()
  }
  isEditHint.value = false
  demansTransLate.value = ''
  shootingTransLateSuggestion.value = ''
  resetFrom()
}
const nextLoading = ref(false)
function getDetails(id, type = '') {
  if (type == 'next') {
    nextLoading.value = true
    if (savedUnwatch.value) {
      savedUnwatch.value()
    }
    isEditHint.value = false
    resetFrom()
  }
  loading.value = true
  getVideoOrderDetail(id)
    .then(res => {
      if (typeof res.data == 'string') {
        return ElMessage.error(res.data)
      }
      isShowAudit.value = false
      orderId.value = id
      prevVideoId.value = res.data.prevVideoId
      nextVideoId.value = res.data.nextVideoId
      orderStatus.value = res.data.status

      // 获取缓存参数
      if (dialogType.value === 'audit' && getCacheParams()) {
        watchFormChanges()
        return
      }
      let filePicList = []
      if (res.data.productPic && res.data.productPic.length > 0) {
        const tempData = {
          url: res.data.productPic ? res.data.productPic : '',
          name: res.data.productPic ? res.data.productPic : '',
          id: undefined,
          picUrl: res.data.productPic ? res.data.productPic : '',
        }
        if (tempData.url && tempData.url != null) {
          filePicList = [tempData]
        }
      }
      let referencePic = []
      if (res.data.referencePic && res.data.referencePic.length) {
        referencePic = res.data.referencePic.map(item => ({
          url: item,
          picUrl: item,
        }))
      }
      let modelTypeList = []
      res.data.modelType = res.data.modelType + ''
      if (res.data.modelType == '3') {
        modelTypeList = ['0', '1']
        temporaryChangeData.value.modelTypeList = ['0', '1']
      } else if (res.data.platform == Platform['Amazon'] && res.data.shootingCountry == '7') {
        modelTypeList[0] = res.data.modelType + ''
        temporaryChangeData.value.modelTypeList[0] = res.data.modelType + ''
      } else {
        modelTypeList = [res.data.modelType]
      }
      if (
        res.data.orderVideoCautionsVO &&
        res.data.orderVideoCautionsVO.cautions &&
        res.data.orderVideoCautionsVO.cautions.length > 0
      ) {
        let cautionsText = res.data.orderVideoCautionsVO.cautions.map((item, i) => item.content).join('\n')
        form.value.cautionsTemp = cautionsText
      }
      if (
        res.data.orderVideoCautionsVO &&
        res.data.orderVideoCautionsVO.cautionsPics &&
        res.data.orderVideoCautionsVO.cautionsPics.length > 0
      ) {
        imageList.value = res.data.orderVideoCautionsVO.cautionsPics.map(item => ({
          picUrl: item,
          url: item,
        }))
      } else {
        imageList.value = []
      }
      emphasisImageList.value = []
      if (res.data.particularEmphasisPic && res.data.particularEmphasisPic.length > 0) {
        emphasisImageList.value = res.data.particularEmphasisPic.map(item => ({
          picUrl: item,
          url: item,
        }))
      } else {
        emphasisImageList.value = []
      }

      form.value = {
        videoCode: res.data.videoCode,
        platform: res.data.platform + '',
        videoFormat: res.data.videoFormat,
        shootingCountry: res.data.shootingCountry + '',
        modelType: res.data.modelType,
        modelTypeList,
        productChinese: res.data.productChinese,
        isLink: res.data.productLink ? 1 : 0,
        productEnglish: res.data.productEnglish,
        productLink: res.data.productLink,
        referenceVideoLink: res.data.referenceVideoLink,
        picCount: res.data.picCount,
        selModelInfo: res.data.intentionModel ? res.data.intentionModel : {},
        shootRequired: res.data.shootRequired || [],
        productPic: res.data.productPic,
        referencePic,
        filePicList,
        demands: '',
        videoDuration: res.data.platform == '0' ? 120 : res.data.platform == '1' ? 90 : 1,
        videoDurationCustom: 120,
        clipsRequiredTemp: '',
        cautionsTemp: form.value.cautionsTemp,
        cautionsPics: [],
        particularEmphasisPic: [],
        orderVideoCautionsVO: res.data.orderVideoCautionsVO,
        productCategory: res.data.productCategory ? res.data.productCategory : '',
        sellingPointProduct: res.data.sellingPointProduct || '',
        orderSpecificationRequire: res.data.orderSpecificationRequire || '',
        particularEmphasis: res.data.particularEmphasis || '',
        videoStyle: res.data.videoStyle * 1 || 0,
        isGund: res.data.isGund === 3 || res.data.isGund === null ? '' : res.data.isGund * 1,
        createOrderUserName: res.data.createOrderUserName || '',
        createOrderUserNickName: res.data.createOrderUserNickName || '',
        videoPromotionAmount: res.data.videoPromotionAmount || 0,
        orderDiscountDetailVOS: res.data.orderDiscountDetailVOS || [],
      }
      productLinkImg.value = res.data.productLink ? res.data.productPic : null
      addPicNumber.value = form.value.picCount == 2 ? 5 : 2
      temporaryChangeData.value.modelType = form.value.modelType
      temporaryChangeData.value.platform = form.value.platform
      temporaryChangeData.value.shootingCountry = form.value.shootingCountry

      if (res.data.sellingPointProduct) doTranslate(2)
      // 视频时长
      if (type == 'edit') {
        if (res.data.videoDuration === 120) {
          form.value.videoDuration = 120
        } else if (res.data.videoDuration === 90) {
          form.value.videoDuration = 90
        } else {
          form.value.videoDuration = 1
          form.value.videoDurationCustom = res.data.videoDuration || 120
        }
      }
      let clipsRequiredText = ''
      // 剪辑要求
      if (res.data.clipsRequired?.length) {
        clipsRequiredText = res.data.clipsRequired.map((item, i) => item.content).join('\n')
        form.value.clipsRequiredTemp = clipsRequiredText
        form.value.clipsRequired = res.data.clipsRequired
      }
      if (res.data.productCategory?.length) {
        form.value.productCategory = res.data.productCategory.map(item => item.id)
      }
      if (res.data.shootRequired && res.data.shootRequired.length > 0) {
        form.value.demands = res.data.shootRequired.map(item => item.content).join('')
        doTranslate(1)
      }
      watchFormChanges()
    })
    .catch(e => {
      console.log(e)
      close()
    })
    .finally(() => {
      loading.value = false
      setTimeout(() => {
        nextLoading.value = false
      }, 500)
    })
}

// 获取缓存表单
const AUDIT_ORDER_PARAMS = 'auditOrderParams'
function getCacheParams() {
  let cacheParams = localStorage.getItem(AUDIT_ORDER_PARAMS)
  if (cacheParams) {
    cacheParams = JSON.parse(cacheParams)
    if (cacheParams[orderId.value]) {
      if (cacheParams[orderId.value].form) {
        form.value = cacheParams[orderId.value].form
        productLinkImg.value = cacheParams[orderId.value].form.productPic
        if (form.value.demands) {
          isShowEditShootRequired.value = true
        }
      }
      if (cacheParams[orderId.value].imageList) {
        imageList.value = cacheParams[orderId.value].imageList
      }
      if (cacheParams[orderId.value].emphasisImageList) {
        emphasisImageList.value = cacheParams[orderId.value].emphasisImageList
      }
      if (cacheParams[orderId.value].demansTransLate) {
        demansTransLate.value = cacheParams[orderId.value].demansTransLate
      }
      if (cacheParams[orderId.value].shootingTransLateSuggestion) {
        shootingTransLateSuggestion.value = cacheParams[orderId.value].shootingTransLateSuggestion
      }

      return true
    }
  }
  return false
}
// 清除缓存表单
function clearCacheParams() {
  let cacheParams = localStorage.getItem(AUDIT_ORDER_PARAMS)
  if (cacheParams) {
    cacheParams = JSON.parse(cacheParams)
    if (cacheParams[orderId.value]) {
      delete cacheParams[orderId.value]
    }
    localStorage.setItem(AUDIT_ORDER_PARAMS, JSON.stringify(cacheParams))
  }
}

//获取品类
function getCategoryList() {
  modelCategorySelectRank({ rank: 1, categoryId: 1008 }).then(res => {
    productCategoryList.value = res.data
  })
}

//有链接无链接切换
function doChangeIsLink() {
  form.value.productPic = productLinkImg.value ? productLinkImg.value : null
}

//翻译
const isShowTranslateContent = ref(false)
const transformContentList = ref([])
const cautionsTempRef = ref(null)
const clipsRequiredTempRef = ref(null)
const demandsRef = ref(null)
//初始化操作
function resetFrom() {
  formRef.value.resetFields()
  shootingTransLateSuggestion.value = ''
  demansTransLate.value = ''
  isShowTranslateContent.value = false
  isShowEditShootRequired.value = false
  cautionsTempRef.value?.resetValue()
  clipsRequiredTempRef.value?.resetValue()
  demandsRef.value?.resetValue()
}

//自定义弹窗
const userDefineType = ref('')
const userDefineDialog = ref(false)
const userDefineForm = ref({
  cnName: '',
  enName: '',
})
const userDefineRules = {
  cnName: [{ required: true, message: '请填写名称字段', trigger: 'blur' }],
  enName: [{ required: true, message: '请填写字段英文名', trigger: 'blur' }],
}
const userDefineRef = ref()
function handleDefined(type) {
  userDefineType.value = type
  userDefineDialog.value = true
}
function closeUserDefineDialog() {
  userDefineForm.value.cnName = ''
  userDefineForm.value.enName = ''
  userDefineRef.value?.resetFields()
  userDefineDialog.value = false
}
function handleUserDefineForm() {
  userDefineRef.value?.validate(valid => {
    if (valid) {
      //   if (userDefineType.value == 'scene') {
      const params = {
        cn: userDefineForm.value.cnName,
        en: userDefineForm.value.enName,
        id: 7,
        value: 8,
      }
      shootRequiredList.value.push(params)
      shootRequired.value.push(params.value)
      //   }
      closeUserDefineDialog()
    }
  })
}

function changePicSuccess(data) {
  if (DragUploadDialogTitle.value == '') {
    dragUploadLimit.value = imgLimit.value
    emphasisImageList.value.push(...data)
    // imageList.value.push(...data)
  } else {
    dragUploadLimit.value = 1
    if (form.value.isLink == 0) {
      // form.value.filePicList.push(...data)
      form.value.filePicList = data
      form.value.productPic = data[0].picUrl
    } else {
      form.value.productPic = data.length > 0 ? data[0].picUrl : ''
      productLinkImg.value = data.length > 0 ? data[0].picUrl : ''
    }
  }
}

//更换产品图与上传图片
function handleDragUploadDialogOpen(type) {
  if (type == 'cautionsImg') {
    DragUploadDialogTitle.value = ''
    dragUploadLimit.value = imgLimit.value
  } else if (type == 'particularEmphasisPic') {
    DragUploadDialogTitle.value = ''
    dragUploadLimit.value = imgLimit.value
  } else {
    dragUploadLimit.value = 1
    DragUploadDialogTitle.value = '更换产品图'
  }
  DragUploadDialogRef1.value.open()
}

//判断模特类型是否可以多选
const isMultiSelModel = computed(() => {
  if (form.value.shootingCountry == '7' && form.value.platform == Platform['Amazon'] && !form.value.picCount)
    return true
  else {
    formRef.value?.clearValidate('modelTypeList')
    return false
  }
})

const oldModelType = ref([])
watch(
  () => form.value.modelTypeList,
  (newVal, oldVal) => {
    if (oldVal && oldVal.length > 0) {
      oldModelType.value = oldVal
    }
  }
)
//删除模特
function removeSelModel() {
  ElMessageBox.confirm('确定删除该意向模特吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(() => {
      form.value.selModelInfo = {}
    })
    .catch(() => {})
}

//模特信息弹窗
const ModelInfoDialogRef = ref()
function showModelInfoDialog(id) {
  ModelInfoDialogRef.value?.open(id)
}

//更换模特
function changeSelModel() {
  if (isMultiSelModel.value && form.value.modelTypeList && form.value.modelTypeList.length == 0) {
    ElMessage.warning('请选择模特类型')
    return
  }
  SelectSingleModelRef.value.open({
    filterBlackListBizUserId: createUserId.value,
  })
}

function changeModelValue(value) {
  if (value && value.length > 0) {
    value.indexOf(0) != -1 ? (form.value.isLink = 1) : ''
  }

  if (value.length == 1 || value.length == 0) {
    form.value.modelType = value[0]
    if (
      form.value.selModelInfo &&
      form.value.selModelInfo.id &&
      form.value.selModelInfo.type != form.value.modelType
    ) {
      ElMessageBox.confirm('', {
        customStyle: {
          'border-radius': '10px',
        },
        message: h('div', null, [
          h('div', { style: 'text-align: center;' }, `变更模特类型会导致已选模特无法满足拍摄需求`),
          h('div', { style: 'text-align: center;' }, `需重新选择模特`),
        ]),
        confirmButtonText: '好的',
        cancelButtonText: '取消',
        center: true,
        roundButton: true,
      })
        .then(() => {
          form.value.selModelInfo = {}
        })
        .catch(() => {
          form.value.modelTypeList = oldModelType.value
          if (form.value.modelTypeList && form.value.modelTypeList.length > 0) {
            form.value.modelTypeList.length == 2
              ? (form.value.modelType = '3')
              : (form.value.modelType = form.value.modelTypeList[0])
          }
        })
    }
  }
  if (value.length == 2) {
    form.value.modelType = '3'
  }
}

// 表单联动 模特类型禁用
const modelTypeDisabled = computed(() => {
  if (form.value.platform != '0' || form.value.picCount) {
    // 平台不是亚马逊
    return true
  } else if (form.value.shootingCountry != '7') {
    // 平台亚马逊且国家不是美国
    return true
  }
  // if (form.value.isLink == 0) {
  //   // 产品无链接
  //   return true
  // }
  return false
})

function handleSelectModelInfo(data) {
  const tempInfo = { ...data }
  tempInfo.pic = tempInfo.picUrl
  form.value.selModelInfo = tempInfo
}

//切换的一些联动
let content = ''
function changeFormValue(value, type) {
  let isConfirm = false
  if (!isMultiSelModel.value) {
    form.value.modelTypeList = ['1']
  }
  if (type == 'platform') {
    content = '平台'
    isConfirm = handleModelPlatform(value)
  } else if (type == 'modelType') {
    content = '模特类型'
    if (form.value.selModelInfo.type != form.value.modelType && form.value.modelType != 3) {
      isConfirm = true
    }
  } else {
    content = '拍摄国家'
    isConfirm = true
  }
  if (form.value.selModelInfo && form.value.selModelInfo.id && isConfirm) {
    ElMessageBox.confirm('', {
      customStyle: {
        'border-radius': '10px',
      },
      message: h('div', null, [
        h('div', { style: 'text-align: center;' }, `变更${content}会导致已选模特无法满足拍摄需求`),
        h('div', { style: 'text-align: center;' }, `需重新选择模特`),
      ]),
      confirmButtonText: '好的',
      cancelButtonText: '取消',
      center: true,
      roundButton: true,
    })
      .then(() => {
        form.value.selModelInfo = {}
        if (type == 'platform') {
          selectPlatformChange(value)
          temporaryChangeData.value.platform = value + ''
        } else if (type == 'shootingCountry') {
          selectNationChange(value)
          temporaryChangeData.value.shootingCountry = value + ''
        } else {
          temporaryChangeData.value.modelType = value + ''
          selectModelTypeChange(value)
        }
      })
      .catch(() => {
        if (type == 'platform') {
          form.value.platform = temporaryChangeData.value.platform
        } else if (type == 'shootingCountry') {
          form.value.shootingCountry = temporaryChangeData.value.shootingCountry
        } else {
          form.value.modelType = temporaryChangeData.value.modelType
        }
        // form.value.modelTypeList = oldModelType.value
        // if (form.value.modelTypeList && form.value.modelTypeList.length > 0) {
        //   form.value.modelTypeList.length == 2
        //     ? (form.value.modelType = '3')
        //     : (form.value.modelType = form.value.modelTypeList[0])
        // }
      })
  } else {
    if (type == 'platform') {
      selectPlatformChange(value)
      temporaryChangeData.value.platform = value + ''
    } else if (type == 'shootingCountry') {
      selectNationChange(value)
      temporaryChangeData.value.shootingCountry = value + ''
    } else {
      temporaryChangeData.value.modelType = value + ''
      selectModelTypeChange(value)
    }
  }
}
//切换时判断是否有符合条件的模特
function handleModelPlatform(value) {
  let temp = ''
  if (value === Platform['Amazon']) {
    temp = Platform['Amazon']
  }
  if (value === Platform['Tiktok']) {
    temp = Platform['Tiktok']
  }
  if (value === Platform['App']) {
    temp = Platform['App']
  }
  if (value === Platform['Other']) {
    temp = Platform['Other']
  }
  if (form.value.selModelInfo && form.value.selModelInfo.platform) {
    let tempList = form.value.selModelInfo.platform.split(',')
    temp = tempList.find(item => {
      return item == value
    })
  }
  return temp == value ? false : true
}

// 表单联动 平台
function selectPlatformChange(value) {
  if (value === platformEmun.value.Amazon) {
    form.value.videoFormat = 1
    form.value.videoDuration = 120
    disabled_edit.value = false
    form.value.videoStyle = 0
    return
  }
  form.value.videoDuration = 90
  if (value === platformEmun.value.Tiktok) {
    disabled_edit.value = false
    form.value.videoFormat = 2
    form.value.modelType = '1'
    form.value.videoStyle = 1
    return
  }
  if (value === platformEmun.value.App) {
    disabled_edit.value = true
    form.value.videoFormat = 1
    form.value.picCount = undefined
    form.value.modelType = '1'
    form.value.referencePic = []
    form.value.videoStyle = 2
    return
  }
  form.value.videoFormat = 1
  form.value.modelType = '1'

  if (value === platformEmun.value.Other) {
    disabled_edit.value = false
    form.value.videoStyle = 0
    return
  }
}
// 表单联动 拍摄国家
function selectNationChange(value) {
  if (value != '7' && form.value.platform == '0') {
    form.value.modelType = '1'
    disabled_edit.value = false
  }
}
// 表单联动 模特类型
function selectModelTypeChange(value) {
  if (form.value.modelType == '1' && form.value.platform == Platform['Amazon']) {
    disabled_edit.value = false
  } else {
    disabled_edit.value = true
    form.value.picCount = undefined
  }
}

//根据已添加的图片计算剩余可添加图片数量
watch(
  () => form.value.referencePic?.length,
  (newVal, oldVal) => {
    addPicNumber1.value = addPicNumber.value - (newVal || 0)
  }
)
watch(
  () => addPicNumber.value,
  (newVal, oldVal) => {
    addPicNumber1.value = addPicNumber.value - (form.value.referencePic?.length || 0)
  }
)

//展示参考图片
function doShowPicUrl(i) {
  const list = form.value.referencePic?.map(item => item.picUrl) || []
  i ? showViewer(list, { index: i }) : showViewer(list)
}

//删除参考图片
function doDeletePicUrl(index) {
  form.value.referencePic?.splice(index, 1)
}

//参考图片上传回调
function upSuccess(data) {
  form.value.referencePic?.push(...data)
}

const imgLimit = computed(() => {
  let temp = 10
  // if (imageList.value && imageList.value.length > 0) {
  //   temp = 10 - imageList.value.length
  // }
  if (emphasisImageList.value && emphasisImageList.value.length > 0) {
    temp = 10 - emphasisImageList.value.length
  }
  return temp
})

const isShowUpload = ref(false)
function handleShowUpload() {
  isShowUpload.value = !isShowUpload.value
}

//处理inputText的输入框内容转数组
function handleInputText(val) {
  if (val && val.length) {
    let countList = []
    countList = val.split('\n')
    return countList.map((item, index) => {
      // const startIndex = index < 9 ? 2 : 3
      return { content: item, sort: index, type: 1 }
      // return { content: item.slice(startIndex), sort: index, type: 1 }
    })
  } else {
    return []
  }
}

function handleSubmitParams() {
  const { clipsRequiredTemp, cautionsTemp, demands, selModelInfo, ...params } = form.value
  delete params.createOrderUserName
  delete params.createOrderUserNickName
  let referencePic = params.referencePic
  let videoDuration = params.videoDuration
  if (demands && demands.length) {
    const shootRequired = handleInputText(demands)
    const temp = params.shootRequired
    params.shootRequired = shootRequired
  } else {
    params.shootRequired = []
  }
  let c1 = handleInputText(clipsRequiredTemp)
  let c2 = handleInputText(cautionsTemp)
  if (imageList.value && imageList.value.length > 0) {
    imageList.value.forEach(item => {
      params.cautionsPics.push(item.picUrl)
    })
  }
  if (emphasisImageList.value && emphasisImageList.value.length > 0) {
    params.particularEmphasisPic = []
    emphasisImageList.value.forEach(item => {
      params.particularEmphasisPic.push(item.picUrl)
    })
  }
  if (params.clipsRequired?.length) {
    c1 = c1?.map((item, i) => {
      if (params.clipsRequired[i]?.id) {
        return {
          ...params.clipsRequired[i],
          content: item.content,
        }
      }
      return item
    })
  }
  if (params.orderVideoCautionsVO.cautions?.length) {
    c2 = c2?.map((item, i) => {
      if (params.orderVideoCautionsVO.cautions[i]?.id) {
        return {
          ...params.orderVideoCautionsVO.cautions[i],
          content: item.content,
        }
      }
      return item
    })
  }
  params.clipsRequired = c1
  params.cautions = c2
  let intentionModelId
  if (selModelInfo.id) {
    intentionModelId = selModelInfo.id
  }
  if (params.picCount && referencePic && referencePic.length) {
    params.referencePic = referencePic.map(item => {
      if (item.response && item.response.code == 200) {
        return item.response.data.picUrl
      }
      return item.picUrl
    })
  } else {
    params.referencePic = null
  }
  if (params.filePicList && params.filePicList.length > 0) {
    params.productPic = params.filePicList[0].picUrl.startsWith(pathHead)
      ? params.filePicList[0].picUrl.slice(pathHead.length)
      : params.filePicList[0].picUrl
  }
  if (productLinkImg.value) {
    params.productPic = productLinkImg.value ? productLinkImg.value : params.productPic
  }
  if (params.videoDuration === 1) {
    videoDuration = params.videoDurationCustom
  } else {
    videoDuration = params.videoDuration
  }
  delete params.videoPromotionAmount

  params.orderVideoCautionsDTO = {
    cautions: params.cautions,
    cautionsPics: params.cautionsPics,
  }
  return { ...params, intentionModelId, videoDuration }
}

//审核通过
function doSubmit() {
  isShowUpload.value = false
  formRef.value.validate(valid => {
    if (valid) {
      loading.value = true
      let params = handleSubmitParams()
      let hint = ''
      isEditHint.value ? (hint = '您已调整订单信息，是否确认审核通过？') : (hint = '是否确认审核通过？')
      ElMessageBox.confirm(hint, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          editVideoOrder({ ...params, id: orderId.value, auditType: 9 })
            .then(res => {
              if (typeof res.data == 'string') {
                return ElMessage.error(res.data)
              }
              if (res.data && res.data.cannotModel && res.data.cannotModel.length > 0) {
                return ElMessage.warning({ message: '当前意向模特已无法满足拍摄需求', offset: 60 })
              }
              ElMessage.success({ message: '审核成功', offset: 60 })
              clearCacheParams()
              isShowAudit.value = true
              imageList.value = []
              emphasisImageList.value = []
              emits('success')
              nextVideoId.value ? getDetails(nextVideoId.value, 'next') : close()
              // getDetails(orderId.value)
            })
            .catch(e => {})
            .finally(() => {
              loading.value = false
            })
        })
        .catch(() => {
          loading.value = false
        })
    }
  })
}

// 缓存表单
function saveCacheParams() {
  isShowUpload.value = false
  let cacheParams = localStorage.getItem(AUDIT_ORDER_PARAMS)
  if (cacheParams) {
    cacheParams = JSON.parse(cacheParams)
    cacheParams[orderId.value] = {
      form: form.value,
      imageList: imageList.value,
      emphasisImageList: emphasisImageList.value,
      demansTransLate: demansTransLate.value,
      shootingTransLateSuggestion: shootingTransLateSuggestion.value,
    }
  } else {
    cacheParams = {
      [orderId.value]: {
        form: form.value,
        imageList: imageList.value,
        emphasisImageList: emphasisImageList.value,
        demansTransLate: demansTransLate.value,
        shootingTransLateSuggestion: shootingTransLateSuggestion.value,
      },
    }
  }
  localStorage.setItem(AUDIT_ORDER_PARAMS, JSON.stringify(cacheParams))
  ElMessage.success('保存成功')
}

// 编辑保存
function saveEdit() {
  isShowUpload.value = false
  formRef.value.validate(valid => {
    if (valid) {
      loading.value = true
      let params = handleSubmitParams()
      editMatchVideoOrder({ ...params, id: orderId.value })
        .then(res => {
          if (typeof res.data == 'string') {
            return ElMessage.error(res.data)
          }
          if (res.data && res.data.cannotModel && res.data.cannotModel.length > 0) {
            return ElMessage.warning('当前意向模特已无法满足拍摄需求')
          }
          ElMessage.success('保存成功')
          emits('success')
          close()
        })
        .catch(e => {})
        .finally(() => {
          loading.value = false
        })
    }
  })
}

const handleDiscount = (list, type) => {
  return list.find(item => item.type == type).discountAmount
}
const handleShowDiscount = (list, type) => {
  return list && list.length > 0 ? list.some(item => item.type == type) : false
}

// const handleClose = done => {
//   ElMessageBox.confirm('Are you sure you want to close this?')
//     .then(() => {
//       done()
//     })
//     .catch(() => {
//       // catch error
//     })
// }
defineExpose({ close, open })
</script>

<style scoped lang="scss">
@import '@/assets/styles/customForm.scss';
@import '@/assets/styles/form-disabled.scss';

:deep(.el-drawer__body) {
  padding: 0 20px;
}
:deep(.el-drawer__header) {
  margin-bottom: 20px;
}
.drawer-box {
  background: #fff;
  border-radius: 4px;
  padding: 10px 15px;

  .img-box {
    text-align: center;
    padding: 0 20px;
    &-tip {
      font-size: 0.6rem;
      color: #999;
      margin-top: 5px;
      text-align: center;
    }
  }

  .box-title {
    padding-left: 10px;
    font-size: 14px;
    &-right {
      background-color: #f5bbc2;
      padding: 3px 8px;
      border-radius: 8px;
      color: #7f7f7f;
    }
  }
  .group-box {
    margin-top: 10px;
    display: flex;
    align-items: flex-start;
  }
}
.el-col-8,
.el-col-12,
.el-col-24 {
  :deep(.el-form-item) {
    flex: 1;
    margin-bottom: 0;
    .el-form-item__label {
      color: #7f7f7f;
      font-weight: 400;
      font-size: 15px;
    }
    .el-form-item__content {
      font-size: 15px;
      margin-bottom: 0;
    }
  }
  display: flex;
  align-items: baseline;
  font-size: 15px;
  margin-bottom: 15px;
  // align-items: center;

  .label {
    color: #7f7f7f;
    flex-shrink: 0;
    width: 100px;
    text-align: left;
  }
  .content {
    flex-shrink: 0;
    width: calc(100% - 100px);
    word-break: break-all;
    flex-wrap: wrap;
    // cursor: pointer;
  }
}
//上传样式
.image-upload {
  box-sizing: border-box;
  display: flex;
  justify-content: center;
  align-items: center;
  border: 1px dashed var(--el-border-color-darker);
  width: 70px;
  height: 70px;
  border-radius: 6px;
  background-color: var(--el-fill-color-lighter);
  cursor: pointer;
  margin-bottom: 10px;
  // margin-top: -10px;
  &:hover {
    border-color: #409eff;
  }
}
.image-list {
  height: 70px;
  position: relative;
  margin-right: 10px;
  margin-bottom: 10px;
  .image-modal {
    position: absolute;
    top: 0;
    left: 0;
    width: 70px;
    height: 100%;
    border-radius: 6px;
    background-color: #2c2b2b66;
    z-index: 9;
    opacity: 0;
    &:hover {
      opacity: 1;
    }
  }
}
.image-item {
  border-radius: 6px;
  box-sizing: border-box;
  width: 70px;
  height: 70px;
  // margin-right: 10px;
}
</style>
