<template>
  <div class="order-info-box">
    <div class="flex-between head-box">
      <div class="flex-around left-box">
        <div class="head-label">
          <span>订单号：</span>
          {{ data.orderNum }}
        </div>
        <div class="head-label">
          <span>
            {{
              (data.merchantInfo?.businessName || '') +
              (data.merchantInfo?.memberCode ? `(${data.merchantInfo.memberCode})` : '')
            }}
          </span>
        </div>
        <div class="head-label">
          <span>下单时间：</span>
          {{ data.orderTime }}
        </div>
        <div class="head-label" v-if="handleShowPayTime()">
          <span>支付时间：</span>
          {{ data.payTime }}
        </div>
        <div class="head-label" v-else>
          <span>审核时间：</span>
          {{ data.auditStatus != 2 ? data.auditTime : '' }}
        </div>
      </div>
      <div class="flex-start gap-10">
        <div class="small-box-btn">
          <DownloadBtn
            type="primary"
            v-btn
            size="small"
            plain
            round
            v-if="checkPermi(['order:manage:expense-invoice'])"
            text="下载费用清单"
            message="确认下载费用清单"
            loadingText="下载中"
            url="/order/pay/download-pay-info"
            :params="() => ({ orderNum: data.orderNum })"
          />
        </div>
        <div
          v-if="
            checkPermi(['order:manage:rate']) &&
            data.defaultExchangeRate &&
            data.orderVideoVOS[0].status != orderStatusMap['交易关闭']
          "
          style="color: #d9001b; display: flex; align-items: center; cursor: pointer"
          @click="handleAction('汇率异常调整', data)"
        >
          <el-icon size="16"><WarnTriangleFilled /></el-icon>
          汇率异常调整
        </div>
        <div class="head-label">
          <span v-if="data.payTime && data.payType !== null">{{ payTypeMap[data.payType] }}</span>
        </div>
        <div class="flex-start small-box-btn" v-if="headActionIds">
          <el-button
            :disabled="isRefund(tableData.orderVideoRefund)"
            v-btn
            size="small"
            round
            type="primary"
            v-if="checkPermi(['order:manage:cancel'])"
            @click="handleAction('取消订单', tableData[0])"
          >
            取消订单
          </el-button>
        </div>
      </div>
    </div>
    <div class="table-box">
      <el-table ref="tableRef" :data="tableData" style="width: 100%" border row-key="id">
        <!-- :header-cell-class-name="headerCheckboxStyle" -->
        <!-- @selection-change="handleSelectionChange" -->
        <template #empty>
          <el-empty description="暂无数据" :image-size="80"></el-empty>
        </template>

        <!-- <el-table-column
          type="selection"
          align="center"
          width="55"
          :selectable="row => row.status === orderStatusMap['待匹配']"
        /> -->

        <el-table-column
          prop="productPicUrl"
          label="产品图"
          align="center"
          width="130"
          class-name="product-img-box"
        >
          <template v-slot="{ row }">
            <div class="flex-start top-tag">
              <el-tag v-if="row.isCare" type="danger" size="small" round>照顾单</el-tag>
              <el-tag v-if="row.carryType == 1" type="danger" size="small" round>主携带</el-tag>
              <el-tag v-if="row.carryType == 2" type="danger" size="small" round>被携带</el-tag>
            </div>
            <div :style="{ 'padding-top': row.status === orderStatusMap['待确认'] ? '20px' : '0px' }">
              <el-image
                style="width: 90px; height: 90px; cursor: pointer"
                :src="
                  row.productPic
                    ? $picUrl +
                      row.productPic +
                      '?imageMogr2/format/webp/interlace/1/quality/100/interlace/0/thumbnail/200x'
                    : ''
                "
                fit="scale-down"
                preview-teleported
                @click="() => row.productPic && showViewer([$picUrl + row.productPic])"
              >
                <template #error>
                  <div class="productPic-img">
                    <img
                      :src="$picUrl + 'static/assets/no-img.png'"
                      alt=""
                      style="width: 100%; height: 100%"
                    />
                    <el-button
                      class="productPic-btn"
                      v-if="
                        row.regain &&
                        row.platform == 0 &&
                        row.productLink &&
                        row.productLink.startsWith('https://www.amazon.com')
                      "
                      v-btn
                      type="primary"
                      size="small"
                      round
                      :loading="row.regainLoading"
                      @click="handleAction('重新获取产品图', row)"
                    >
                      重新获取
                    </el-button>
                  </div>
                  <!-- <div class="flex-column" style="height: 100%; justify-content: center; gap: 10px">
                  <el-icon :size="30" color="#ccc"><Picture /></el-icon>
                </div> -->
                </template>
              </el-image>
              <template v-if="row.status === orderStatusMap['待确认']">
                <el-button
                  v-if="row.contact?.id && checkPermi(['order:manage:upload-productPic'])"
                  v-btn
                  link
                  :disabled="isRefund(row.orderVideoRefund)"
                  type="primary"
                  @click="handleAction('上传产品图', row)"
                >
                  上传产品图
                </el-button>
                <el-button
                  v-else-if="checkPermi(['order:manage:upload-productPic'])"
                  v-btn
                  link
                  :disabled="isRefund(row.orderVideoRefund)"
                  type="primary"
                  @click="checkContact()"
                >
                  上传产品图
                </el-button>
              </template>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="msg" label="产品信息" minWidth="405">
          <template v-slot="{ row }">
            <div class="product-info-box">
              <div class="flex-between">
                <!-- <span>对接客服：{{ row.contact?.name }}</span> -->
                <div class="flex-center">
                  <span>视频编码：{{ row.videoCode }}</span>
                  <div
                    class="flex-center discount-box"
                    v-if="handleShowDiscount(row.orderDiscountDetailVOS, '1')"
                  >
                    <div class="discount-box__left">满</div>
                    <div class="discount-box__right">
                      -{{ handleDiscount(row.orderDiscountDetailVOS, '1') }}
                    </div>
                  </div>
                  <div
                    class="flex-center discount-box"
                    v-if="handleShowDiscount(row.orderDiscountDetailVOS, '4')"
                  >
                    <div class="discount-box__left">首</div>
                    <div class="discount-box__right">
                      -${{ handleDiscount(row.orderDiscountDetailVOS, '4') }}
                    </div>
                  </div>
                </div>

                <div>
                  <CopyButton
                    v-if="row.shootModel"
                    class="btn"
                    size="small"
                    plain
                    :async-copy-content="() => handleCopyshootModelLink(row)"
                  >
                    链接
                  </CopyButton>
                  <el-button class="btn" size="small" v-btn plain @click="handleAction('更多', row)">
                    更多
                  </el-button>
                  <CopyButton class="btn" plain :copy-content="handleCopy(row)" />
                </div>
              </div>
              <!-- <div>视频编码：{{ row.videoCode }}</div> -->
              <div class="one-ell">中文名称：{{ row.productChinese }}</div>
              <div class="one-ell" style="word-break: break-all">英文名称：{{ row.productEnglish }}</div>
              <div class="one-ell productLink">
                产品链接：
                <el-link :underline="false" target="_blank" type="primary" :href="row.productLink">
                  {{ row.productLink }}
                </el-link>
              </div>
              <!-- <div>
                <el-button v-btn link type="primary" @click="handleAction('更多', row)">更多></el-button>
              </div> -->
              <biz-model-platform :value="row.platform" />
              <biz-model-type :value="row.modelType" />
              <biz-nation :value="row.shootingCountry" />
              <template v-for="op in videoFormatOptions" :key="op.value">
                <el-tag class="tag" v-if="op.value == row.videoFormat" type="warning" size="small" round>
                  {{ op.label }}
                </el-tag>
              </template>
              <el-tag
                class="tag"
                v-if="
                  row.status !== orderStatusMap['待确认'] &&
                  row.status !== orderStatusMap['待支付'] &&
                  row.status !== orderStatusMap['待审核']
                "
                :type="row.isGund === 0 ? 'success' : row.isGund === 1 ? 'warning' : 'info'"
                size="small"
                round
                style="margin-left: 5px"
              >
                {{ row.isGund === 0 ? '非通品' : row.isGund === 1 ? '通品' : '暂未选择' }}
              </el-tag>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="picCount" label="照片数量" align="center" width="110">
          <template v-slot="{ row }">
            <div>{{ handleSelectiveAssembly(row.picCount) }}</div>
            <el-button
              v-btn
              v-if="row.referencePic && row.referencePic?.length"
              link
              type="primary"
              @click="handleAction('已选参考图', row)"
            >
              已选参考图
            </el-button>
            <el-tag type="info" size="small" v-if="handleOrderVideoRefundList(row.orderVideoRefundList, 1)">
              已退款{{ row.refundPicCount > 0 ? `(${row.refundPicCount}张)` : '' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="user" label="订单运营" align="center" width="150">
          <template v-slot="{ row }">
            <!-- <div v-if="row.createOrderUserInfo?.createOrderUserAccount === row.createOrderUserInfo?.ownerAccount">
              {{ row.createOrderUserInfo?.createOrderUserNickName || '-' }}
            </div> -->
            <div>
              <div>姓名：{{ row.createOrderUserName || '-' }}</div>
              <div>微信名：{{ row.createOrderUserNickName || '-' }}</div>
              <!-- {{ row.createOrderUserName ? row.createOrderUserName : row.createOrderUserNickName }} -->
            </div>
            <!-- <div>
              {{ row.createOrderUserAccount ? `(ID:${row.createOrderUserAccount})` : '' }}
            </div> -->
          </template>
        </el-table-column>
        <el-table-column prop="model" label="意向模特" align="center" width="130">
          <template v-slot="{ row }">
            <ModelCardItem
              v-if="row.intentionModel"
              :data="row.intentionModel"
              @mouseenter="handleMouseEnter($event, row.intentionModel.id)"
              @mouseleave="handleMouseLeave($event, row.intentionModel.id)"
              style="padding: 10px 0"
            />
            <div v-else>-</div>
          </template>
        </el-table-column>
        <el-table-column prop="preselectModelId" label="预选模特" align="center" width="110">
          <template v-slot="{ row }">
            <template v-if="row.status === orderStatusMap['待匹配']">
              <div class="flex-column gap-5" style="padding: 10px 0">
                <!-- <el-button
                  v-btn
                  v-if="row.preselectModel === null"
                  @click="handleAction('预选管理-in', row)"
                  type="primary"
                  icon="Plus"
                  circle
                /> -->
                <el-tooltip v-if="row.videoMatchOrderVO?.status == 2" placement="top" trigger="hover">
                  <template #content>
                    <div style="max-width: 500px">{{ row.videoMatchOrderVO?.pauseReason }}</div>
                  </template>
                  <div style="color: #bb0000">暂停匹配</div>
                </el-tooltip>
                <template v-if="row.videoMatchOrderVO?.normalVideoMatchPreselectModelOrderVOS?.length">
                  <div v-if="row.videoMatchOrderVO.normalVideoMatchPreselectModelOrderVOS[0].status == 2">
                    <div class="preselect-model-corner-mark"></div>
                    <template
                      v-for="(item, i) in row.videoMatchOrderVO.normalVideoMatchPreselectModelOrderVOS"
                    >
                      <ModelCardItem
                        v-if="i == 0"
                        :data="item.model"
                        @mouseenter="handleMouseEnter($event, item.model.id)"
                        @mouseleave="handleMouseLeave($event, item.model.id)"
                      />
                    </template>
                  </div>
                  <div class="flex-center" v-else>
                    <PreselectModelImage
                      :list="row.videoMatchOrderVO.normalVideoMatchPreselectModelOrderVOS"
                    />
                  </div>
                </template>
                <div v-else>暂无预选</div>
                <el-button
                  v-if="checkPermi(['order:manage:preselection'])"
                  v-btn
                  style="margin-left: 0"
                  link
                  type="primary"
                  @click="handleAction('查看预选', row)"
                >
                  查看预选
                </el-button>
              </div>
            </template>
            <div class="flex-column" v-else>
              <div
                v-if="row.videoMatchOrderVO?.normalVideoMatchPreselectModelOrderVOS?.length"
                class="flex-center"
              >
                <PreselectModelImage :list="row.videoMatchOrderVO.normalVideoMatchPreselectModelOrderVOS" />
              </div>
              <span v-else>-</span>
              <el-button
                v-if="
                  row.videoMatchOrderVO?.normalVideoMatchPreselectModelOrderVOS?.length &&
                  (row.status === orderStatusMap['需发货'] ||
                    row.status === orderStatusMap['待完成'] ||
                    row.status === orderStatusMap['需确认'] ||
                    row.status === orderStatusMap['交易关闭'] ||
                    row.status === orderStatusMap['已完成']) &&
                  checkPermi(['order:manage:preselection'])
                "
                v-btn
                style="margin-left: 0"
                link
                type="primary"
                @click="handleAction('查看预选', row)"
              >
                查看预选
              </el-button>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="model2"
          label="拍摄模特"
          align="center"
          width="130"
          class-name="shoot-model-box"
        >
          <template v-slot="{ row }">
            <div>
              <div
                class="hint-box"
                style="pointer-events: none"
                v-if="row.intentionModel?.account && row.intentionModel?.account != row.shootModel?.account"
              >
                <div class="exchange">
                  <span>换</span>
                </div>
              </div>
              <ModelCardItem
                v-if="row.shootModel"
                :data="row.shootModel"
                @mouseenter="handleMouseEnter($event, row.shootModel.id)"
                @mouseleave="handleMouseLeave($event, row.shootModel.id)"
                style="padding: 10px 0"
              />
              <div v-else>-</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="contact" label="中文部/英文部" align="center" width="110">
          <template v-slot="{ row }">
            <div>
              <!-- {{ handleContactAndIssue(row.contact, row.issue) }} -->
              <div>{{ row.contact?.name || '-' }} / {{ row.issue?.name || '-' }}</div>
              <!-- <div v-if="row.status === orderStatusMap['待匹配'] && row.statusTime">
                确认时间：{{ row.statusTime }} ({{ handleOrderStatusTime(row.statusTime) }})
              </div>
              <div>/</div> -->
              <!-- <div>{{ row.issue?.name || '-' }}</div> -->
              <!-- <div
                v-if="
                  row.status === orderStatusMap['需发货'] ||
                  (row.status === orderStatusMap['需确认'] && row.statusTime)
                "
              >
                提交时间：{{ row.statusTime }} ({{ handleOrderStatusTime(row.statusTime) }})
              </div> -->
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="status"
          label="订单状态"
          align="center"
          width="230"
          class-name="product-img-box"
        >
          <template v-slot="{ row }">
            <div v-if="row.reminder && row.reminder > 0" class="urge-title">催x{{ row.reminder }}</div>
            <div class="top-tag">
              <el-tag v-if="row.rollbackId" type="danger" size="small" round>回退订单</el-tag>
            </div>
            <div v-if="row.orderVideoRefund?.refundStatus == orderRefundStatusMap['退款成功']">
              <el-tag type="danger" size="small">退款成功</el-tag>
            </div>
            <div>
              {{ orderStatusMap[row.status] }}
            </div>
            <!-- <div v-if="row.status === orderStatusMap['待支付'] && row.statusTime">
              ({{ handleOrderStatusTime(row.statusTime) }})
            </div> -->
            <div
              v-if="
                row.status != orderStatusMap['已完成'] &&
                row.status != orderStatusMap['交易关闭'] &&
                row.statusTime
              "
            >
              ({{ handleOrderStatusTime(row.statusTime) }})
            </div>
            <div
              v-if="
                (row.status === orderStatusMap['需发货'] ||
                  row.status === orderStatusMap['需确认'] ||
                  row.status === orderStatusMap['待支付'] ||
                  row.status === orderStatusMap['待审核'] ||
                  row.status === orderStatusMap['待确认'] ||
                  row.status === orderStatusMap['待完成'] ||
                  row.status === orderStatusMap['待匹配']) &&
                row.statusTime
              "
            >
              提交时间：{{ row.statusTime }}
            </div>
            <div v-if="row.status === orderStatusMap['交易关闭'] && row.statusTime">
              关闭时间：{{ row.statusTime }}
            </div>
            <div v-if="row.status === orderStatusMap['已完成'] && row.statusTime">
              完成时间：{{ row.statusTime }}
            </div>

            <!-- </div> -->
            <!-- <el-button v-btn link type="primary" @click="routerNewWindow('/order/details/' + row.id)">
              订单详情
            </el-button> -->
            <!-- <el-button v-btn link type="primary" @click="handleAction('查看详情', row)">订单详情</el-button> -->
            <div>
              <el-tag
                type="info"
                size="small"
                v-if="
                  row.orderVideoRefund?.refundStatus == orderRefundStatusMap['退款待审核'] ||
                  row.orderVideoRefund?.refundStatus == orderRefundStatusMap['退款中']
                "
              >
                {{ orderRefundStatusMap[row.orderVideoRefund.refundStatus] }}
              </el-tag>
            </div>
            <template v-if="row.orderVideoRefundList?.length">
              <div>
                <el-tag
                  type="info"
                  size="small"
                  v-if="handleOrderVideoRefundList(row.orderVideoRefundList, 2)"
                >
                  有补偿订单
                </el-tag>
              </div>
            </template>
          </template>
        </el-table-column>
        <el-table-column prop="logisticInfo" label="物流信息" align="center" width="230">
          <template v-slot="{ row }">
            <div v-if="row.isObject && row.status === orderStatusMap['待完成'] && row.statusTime">
              确认模特：{{ row.statusTime }} ({{ handleOrderStatusTime(row.statusTime) }})
              <div class="logistic-sign-time" v-if="row.logisticFollowVideoInfoVO?.signTime">
                已确认{{ handleOrderStatusTime(row.logisticFollowVideoInfoVO.signTime) }}
              </div>
            </div>
            <template
              v-else-if="
                (row.status === orderStatusMap['待完成'] ||
                  row.status === orderStatusMap['需确认'] ||
                  row.status === orderStatusMap['已完成'] ||
                  row.status === orderStatusMap['交易关闭']) &&
                row.logisticFollowVideoInfoVO
              "
            >
              <div v-if="row.logisticFollowVideoInfoVO.mainStatusSketch != '通知拍摄'">
                <img style="width: 15px" src="@/assets/icons/svg/truck.svg" alt="" />
                &nbsp;{{ row.logisticFollowVideoInfoVO.mainStatusSketch }}
              </div>
              <div
                v-if="
                  row.logisticFollowVideoInfoVO.mainStatusSketch === '通知拍摄' ||
                  ((row.status === orderStatusMap['待完成'] ||
                    row.status === orderStatusMap['需确认'] ||
                    row.status === orderStatusMap['已完成']) &&
                    row.statusTime &&
                    row.isObject != 0)
                "
              >
                确认模特：{{ row.statusTime }} ({{ handleOrderStatusTime(row.statusTime) }})
              </div>
              <div
                v-if="
                  (row.status === orderStatusMap['待完成'] ||
                    row.status === orderStatusMap['需确认'] ||
                    row.status === orderStatusMap['已完成'] ||
                    row.status === orderStatusMap['交易关闭']) &&
                  row.shippingTime &&
                  row.isObject == 0
                "
              >
                发货时间：{{ row.shippingTime }} ({{ handleOrderStatusTime(row.shippingTime) }})
              </div>
              <div class="logistic-sign-time" v-if="row.logisticFollowVideoInfoVO?.signTime">
                <span>{{ row.isObject ? '已确认' : '已签收' }}</span>
                <span>{{ handleOrderStatusTime(row.logisticFollowVideoInfoVO.signTime) }}</span>
              </div>
              <!-- <div v-if="row.status === orderStatusMap['需确认'] || row.status === orderStatusMap['待完成']">
                <el-tag
                  v-if="row.logisticInfo?.receipt == '0' && checkPermi(['order:manage:receipt'])"
                  type="warning"
                  effect="plain"
                  style="cursor: pointer"
                  @click="handleAction('确认收货', row)"
                >
                  确认收货
                </el-tag>
                <el-tag type="info" effect="plain" v-if="row.logisticInfo?.receipt == '1'">已收货</el-tag>
              </div> -->
            </template>
            <div v-else-if="row.logisticFlag == 1">
              <div>标记发货</div>
              <div>
                标记时间：{{ row.logisticFlagTime }}（{{ handleOrderStatusTime(row.logisticFlagTime) }}）
              </div>
            </div>
            <div v-else>-</div>
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="操作" :align="'center'" width="200">
          <template v-slot="{ row }">
            <div class="flex-start gap-5 task-tag-box">
              <div class="tag-clip" v-if="row.hasUnfinishedEditTask"><span>剪辑中</span></div>
              <el-button
                type="info"
                size="small"
                text
                bg
                v-if="row.workOrderTaskStatus === 1"
                @click="toTaskPage('2', row)"
              >
                工单中
              </el-button>
              <el-button
                type="info"
                size="small"
                text
                bg
                v-else-if="row.workOrderTaskStatus === 4"
                @click="toTaskPage('2', row)"
              >
                工单已完结
              </el-button>
              <el-button
                type="info"
                size="small"
                text
                bg
                v-if="row.afterSaleTaskStatus === 1"
                @click="toTaskPage('1', row)"
              >
                售后中
              </el-button>
              <el-button
                type="info"
                size="small"
                text
                bg
                v-else-if="row.afterSaleTaskStatus === 4"
                @click="toTaskPage('1', row)"
              >
                售后已完结
              </el-button>
            </div>
            <template v-if="row.status === orderStatusMap['待支付']">
              <!-- <el-button
                v-btn
                link
                type="primary"
                :disabled="isRefund(row.orderVideoRefund)"
                v-hasPermi="['order:manage:cancel']"
                @click="handleAction('取消订单', row)"
              >
                取消订单
              </el-button> -->
            </template>
            <template v-else-if="row.status == orderStatusMap['待审核']">
              <template v-if="data.payType">
                <el-button
                  v-if="checkPermi(['order:manage:audit'])"
                  v-btn
                  link
                  type="primary"
                  :disabled="isRefund(row.orderVideoRefund)"
                  @click="handleAction('去审核', row)"
                >
                  去审核
                </el-button>
              </template>
              <!-- <el-button
                v-btn
                link
                type="primary"
                :disabled="isRefund(row.orderVideoRefund)"
                v-hasPermi="['order:manage:cancel']"
                @click="handleAction('取消订单', row)"
              >
                取消订单
              </el-button> -->
            </template>
            <template v-else-if="row.status === orderStatusMap['待确认']">
              <el-button
                v-if="checkPermi(['order:manage:edit'])"
                v-btn
                link
                type="primary"
                :disabled="isRefund(row.orderVideoRefund)"
                @click="handleAction('编辑订单', row)"
              >
                审核订单
              </el-button>
              <!-- <el-button
                v-btn
                link
                type="primary"
                :disabled="isRefund(row.orderVideoRefund)"
                v-hasPermi="['order:manage:refund']"
                @click="handleAction('申请退款', row)"
              >
                申请退款
              </el-button> -->
              <!-- <el-upload
                v-if="row.contact?.id"
                v-hasPermi="['order:manage:upload-productPic']"
                action=""
                list-type="picture"
                :disabled="isRefund(row.orderVideoRefund)"
                :auto-upload="false"
                :show-file-list="false"
                :on-change="fileInputChange"
              >
                <el-button
                  v-btn
                  link
                  :disabled="isRefund(row.orderVideoRefund)"
                  type="primary"
                  @click="handleUpAction('上传产品图', row)"
                >
                  上传产品图
                </el-button>
              </el-upload>
              <el-button
                v-else
                v-btn
                v-hasPermi="['order:manage:upload-productPic']"
                link
                :disabled="isRefund(row.orderVideoRefund)"
                type="primary"
                @click="checkContact()"
              >
                上传产品图
              </el-button> -->
            </template>
            <template v-else-if="row.status === orderStatusMap['待匹配']">
              <!-- <el-button
                v-btn
                link
                type="primary"
                :disabled="isRefund(row.orderVideoRefund)"
                v-hasPermi="['order:manage:preselection']"
                @click="handleAction('预选管理', row)"
              >
                预选管理
              </el-button> -->
              <el-button
                v-if="row.videoMatchOrderVO?.status == 1 && checkPermi(['order:manage:stopMatch'])"
                v-btn
                link
                type="primary"
                :disabled="isRefund(row.orderVideoRefund)"
                @click="handleAction('暂停模特匹配', row)"
              >
                暂停模特匹配
              </el-button>
              <el-button
                v-if="row.videoMatchOrderVO?.status == 2 && checkPermi(['order:manage:continueMatch'])"
                v-btn
                link
                type="primary"
                :disabled="isRefund(row.orderVideoRefund)"
                @click="handleAction('继续模特匹配', row)"
              >
                继续模特匹配
              </el-button>
              <el-button
                v-if="checkPermi(['order:manage:editMatchOrder'])"
                v-btn
                link
                type="primary"
                :disabled="isRefund(row.orderVideoRefund)"
                @click="handleAction('修改订单信息', row)"
              >
                修改订单信息
              </el-button>
              <!-- <el-button
                v-btn
                v-if="row.unMatchFlag != 1"
                link
                type="primary"
                :disabled="isRefund(row.orderVideoRefund)"
                v-hasPermi="['order:manage:submit']"
                @click="handleAction('确认提交', row)"
              >
                确认提交
              </el-button> -->
              <el-button
                v-if="checkPermi(['order:manage:case'])"
                v-btn
                link
                type="primary"
                :disabled="isRefund(row.orderVideoRefund)"
                @click="handleAction('匹配情况反馈', row)"
              >
                匹配情况反馈
              </el-button>
              <!-- <el-button
                v-btn
                link
                type="primary"
                :disabled="isRefund(row.orderVideoRefund)"
                v-hasPermi="['order:manage:refund']"
                @click="handleAction('申请退款', row)"
              >
                申请退款
              </el-button> -->
              <!-- <el-button
                v-btn
                link
                type="primary"
                :disabled="isRefund(row.orderVideoRefund)"
                v-hasPermi="['order:manage:flag']"
                @click="handleAction('标记订单', row)"
              >
                标记订单
              </el-button> -->
            </template>
            <template v-else-if="row.status === orderStatusMap['需发货']">
              <el-button
                v-if="checkPermi(['order:manage:editMatchOrder'])"
                v-btn
                link
                type="primary"
                :disabled="isRefund(row.orderVideoRefund)"
                @click="handleAction('修改订单信息', row)"
              >
                修改订单信息
              </el-button>
              <el-button
                v-btn
                v-if="checkPermi(['order:manage:shipments']) && row.isObject === 0"
                link
                type="primary"
                :disabled="isRefund(row.orderVideoRefund)"
                @click="handleAction('填写物流单号', row)"
              >
                {{ row.logisticFlag == '1' ? '填写物流单号' : '去发货' }}
              </el-button>
              <div>
                <!-- <CopyButton class="btn" link type="primary" :copy-content="handleCopyAddress(row)">
                  复制地址
                </CopyButton> -->
                <el-button class="btn" link type="primary" v-btn @click="handleCopyAddress(row)">
                  复制地址
                </el-button>
              </div>
              <!-- <el-button
                v-btn
                link
                type="primary"
                :disabled="isRefund(row.orderVideoRefund)"
                v-hasPermi="['order:manage:refund']"
                @click="handleAction('申请退款', row)"
              >
                申请退款
              </el-button> -->
            </template>
            <template v-else-if="row.status === orderStatusMap['待完成']">
              <el-button
                v-btn
                link
                type="primary"
                v-if="checkPermi(['order:manage:reminder']) && row.reminder && row.reminder > 0"
                @click="handleAction('已催单', row)"
              >
                已催单
              </el-button>
              <el-button
                v-btn
                v-if="checkPermi(['order:manage:shipments']) && row.isObject === 0"
                link
                type="primary"
                :disabled="isRefund(row.orderVideoRefund)"
                @click="handleAction('填写物流单号', row)"
              >
                补发物流
              </el-button>

              <!-- <el-button
                v-btn
                link
                type="primary"
                v-hasPermi="['order:manage:receipt']"
                @click="handleAction('确认收货', row)"
              >
                确认收货
              </el-button> -->

              <!-- <el-button
                v-btn
                link
                type="primary"
                :disabled="isRefund(row.orderVideoRefund)"
                v-hasPermi="['order:manage:refund']"
                @click="handleAction('申请退款', row)"
              >
                申请退款
              </el-button> -->
              <el-button
                v-if="checkPermi(['order:manage:feedbackMaterial'])"
                v-btn
                link
                type="primary"
                :disabled="isRefund(row.orderVideoRefund)"
                @click="handleAction('反馈素材', row)"
              >
                反馈素材给商家
              </el-button>
              <el-button
                v-if="checkPermi(['order:manage:modelFeedbackMaterial'])"
                v-btn
                link
                type="primary"
                :disabled="isRefund(row.orderVideoRefund)"
                @click="handleAction('模特反馈素材', row)"
                :class="{ 'btn-red-tip': row.hasMaterial }"
              >
                模特反馈素材
              </el-button>
              <!-- <el-button v-btn
                link
                type="primary"
                v-hasPermi="['order:manage:feedbackMaterial-download']"
                @click="handleAction('下载反馈素材', row)"
              >
                下载反馈素材
              </el-button> -->
              <el-button
                v-if="checkPermi(['order:manage:create-work-order'])"
                v-btn
                link
                type="primary"
                :disabled="isRefund(row.orderVideoRefund)"
                @click="handleAction('创建工单', row)"
              >
                创建任务单
              </el-button>
              <el-button
                v-if="checkPermi(['order:manage:editMatchOrder'])"
                v-btn
                link
                type="primary"
                :disabled="isRefund(row.orderVideoRefund)"
                @click="handleAction('修改订单信息', row)"
              >
                修改订单信息
              </el-button>
            </template>
            <template v-else-if="row.status === orderStatusMap['已完成']">
              <!-- <el-button
                v-if="row.isUpload == 1 && checkPermi(['order:manage:upload'])"
                v-btn
                link
                type="primary"
                :disabled="isRefund(row.orderVideoRefund)"
                @click="handleAction('上传视频', row)"
              >
                上传视频
              </el-button> -->
              <el-button
                v-if="
                  checkPermi(['order:manage:viewMaterial']) &&
                  (row.uploadStatus == 0 || row.uploadStatus == 1 || row.uploadStatus == 2)
                "
                v-btn
                link
                type="primary"
                @click="handleAction('上传素材', row)"
              >
                查看素材
              </el-button>
              <el-button
                v-if="checkPermi(['order:manage:feedbackMaterial'])"
                v-btn
                link
                type="primary"
                :disabled="isRefund(row.orderVideoRefund)"
                @click="handleAction('反馈素材', row)"
              >
                反馈素材给商家
              </el-button>
              <!-- <el-button
                v-btn
                link
                type="primary"
                :disabled="isRefund(row.orderVideoRefund)"
                v-hasPermi="['order:manage:refund']"
                @click="handleAction('申请退款', row)"
              >
                申请退款
              </el-button> -->
            </template>
            <template v-else-if="row.status === orderStatusMap['需确认']">
              <!-- <el-button
                v-btn
                link
                type="primary"
                v-hasPermi="['order:manage:receipt']"
                @click="handleAction('确认收货', row)"
              >
                确认收货
              </el-button> -->
              <el-button
                v-if="checkPermi(['order:manage:feedbackMaterial'])"
                v-btn
                link
                type="primary"
                :disabled="isRefund(row.orderVideoRefund)"
                @click="handleAction('反馈素材', row)"
              >
                反馈素材给商家
              </el-button>
              <el-button
                v-if="checkPermi(['order:manage:modelFeedbackMaterial'])"
                v-btn
                link
                type="primary"
                :disabled="isRefund(row.orderVideoRefund)"
                @click="handleAction('模特反馈素材', row)"
                :class="{ 'btn-red-tip': row.hasMaterial }"
              >
                模特反馈素材
              </el-button>
              <el-button
                v-if="checkPermi(['order:manage:viewMaterial']) && row.shootModel?.type == 1"
                v-btn
                link
                type="primary"
                @click="handleAction('上传素材', row)"
              >
                上传素材
              </el-button>
              <el-button
                v-if="checkPermi(['order:manage:create-work-order'])"
                v-btn
                link
                type="primary"
                :disabled="isRefund(row.orderVideoRefund)"
                @click="handleAction('创建工单', row)"
              >
                创建任务单
              </el-button>
              <!-- <el-button
                v-btn
                link
                type="primary"
                :disabled="isRefund(row.orderVideoRefund)"
                v-hasPermi="['order:manage:refund']"
                @click="handleAction('申请退款', row)"
              >
                申请退款
              </el-button> -->
            </template>
            <el-button
              v-if="checkPermi(['order:manage:remark'])"
              v-btn
              link
              type="primary"
              @click="handleAction('备注', row)"
              :class="{ 'btn-red-tip': row.hasComment }"
            >
              备注
            </el-button>
            <el-button
              v-if="checkPermi(['order:manage:details'])"
              v-btn
              link
              type="primary"
              @click="routerNewWindow('/order/details/' + row.id)"
            >
              查看详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script setup>
import DownloadBtn from '@/components/Button/DownloadBtn'
import PreselectModelImage from '@/views/order/components/preselectModelImage.vue'
import CopyButton from '@/components/Button/CopyButton.vue'
import ModelCardItem from '@/views/order/components/modelCardItem.vue'
import {
  orderStatusMap,
  picCountOptions,
  orderRefundStatusMap,
  videoFormatOptions,
} from '@/views/order/list/data.js'
import { crawlProductPic, downloadPayInfo, videoOrderDetails } from '@/api/order/order'
import { payTypeMap, addressInfoList } from '@/utils/dict'
import { copy } from '@/utils/index'
import { createBackstageLink } from '@/api/model/model'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useViewer } from '@/hooks/useViewer'
import { checkPermi } from '@/utils/permission'
const { proxy } = getCurrentInstance()
const router = useRouter()

const { showViewer } = useViewer()
const { biz_nation_e, biz_model_type, biz_nation } = proxy.useDict(
  'biz_nation_e',
  'biz_model_type',
  'biz_nation'
)

const props = defineProps({
  data: {
    type: Object,
    required: true,
  },
})
const emits = defineEmits(['action', 'selectionChange', 'uploadChange', 'hover'])

const tableData = computed(() => {
  if (props.data?.orderVideoVOS) {
    return props.data.orderVideoVOS.map(item => {
      item.regain = false
      item.regainLoading = false
      if (props.data.orderTime) {
        if (new Date().getTime() - new Date(props.data.orderTime).getTime() > 1000 * 60 * 5) {
          item.regain = true
        }
      }
      return item
    })
  }
  return []
})

const headActionIds = computed(() => {
  return tableData.value.every(item => item.status === orderStatusMap['待支付'])
})

function headerCheckboxStyle({ row, column, rowIndex, columnIndex }) {
  if (columnIndex === 0 && tableData.value[rowIndex]?.status != orderStatusMap['待匹配']) {
    return 'seltAllbtnDis'
  }
}

function handleShowPayTime() {
  if (props.data.payType) {
    if (
      [
        payTypeMap['银行卡'],
        payTypeMap['银行卡+余额'],
        payTypeMap['对公'],
        payTypeMap['对公+余额'],
        payTypeMap['全币种'],
        payTypeMap['全币种+余额'],
      ].includes(props.data.payType)
    ) {
      return false
    }
  }
  return true
}

function handleOrderStatusTime(time) {
  if (!time) return ''
  // let distance = new Date().getTime() - new Date(time).getTime()
  // let days = 0
  // let hours = Math.floor(distance / (1000 * 60 * 60))
  // days = Math.floor(distance / (1000 * 60 * 60 * 24))
  // hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
  // return (days ? days + '天 ' : '0天') + hours.toString() + '小时'
  let t = time.split(' ')[0]
  let date = new Date(t).getTime()
  let now = new Date().getTime()
  let day = Math.floor((now - date) / (1000 * 60 * 60 * 24))
  return (day > 0 ? day : 0) + '天'
}

function isRefund(orderVideoRefund) {
  return (
    orderVideoRefund != null &&
    orderVideoRefund.refundType == 2 &&
    (orderVideoRefund.refundStatus == 0 ||
      orderVideoRefund.refundStatus == 1 ||
      orderVideoRefund.refundStatus == 4)
  )
}

function handleCopy(row) {
  let str = ''
  if (row.createOrderUserName || row.createOrderUserNickName) {
    str +=
      '订单运营：' + (row.createOrderUserName ? row.createOrderUserName : row.createOrderUserNickName) + '\n'
  }
  if (row.videoCode) {
    str += '视频编码：' + row.videoCode + '\n'
  }
  if (row.productChinese) {
    str += '中文名称：' + row.productChinese + '\n'
  }
  if (row.productEnglish) {
    str += '英文名称：' + row.productEnglish + '\n'
  }
  if (row.productLink) {
    str += '产品链接：' + row.productLink + '\n'
  }
  if (row.shootModel) {
    let type = biz_model_type.value.find(item => item.value == row.shootModel.type)
    let nation = biz_nation.value.find(item => item.value == row.shootModel.nation)
    str += `拍摄模特：${row.shootModel.name} ${type ? '(' + type.label + ')' : ''} ${
      nation ? nation.label : ''
    }`
  }
  return str
}

const addressInfo = ref(addressInfoList[6])
async function handleCopyAddress(row) {
  const res = await videoOrderDetails(row.id)
  let curData = {}
  if (res.data && res.data.orderLogisticSimpleVOS && res.data.orderLogisticSimpleVOS.length > 0) {
    curData = res.data.orderLogisticSimpleVOS[0].shippingInfoSimpleVO
  }
  addressInfo.value = addressInfoList[curData.nation * 1 - 1]
  let nationName = biz_nation_e.value.find(item => item.value == curData.nation)?.label
  let str = ''
  str += addressInfo.value['recipient'] + '：' + curData.recipient + '\n'
  str += addressInfo.value['detailAddress'] + '：' + curData.detailAddress + '\n'
  if (addressInfo.value['city']) str += addressInfo.value['city'] + '：' + curData.city + '\n'
  if (addressInfo.value['province']) str += addressInfo.value['province'] + '：' + curData.state + '\n'
  if (addressInfo.value['state']) str += addressInfo.value['state'] + '：' + curData.state + '\n'
  if (addressInfo.value['localArea']) str += addressInfo.value['localArea'] + '：' + curData.state + '\n'
  str += addressInfo.value['zipCode'] + '：' + curData.zipcode + '\n'
  str += addressInfo.value['nation'] + '：' + nationName + '\n'
  proxy.$modal
    .alert(
      `<div class="template-pre" style="line-height:30px;line-break:anywhere;white-space:pre-wrap;word-break:break-all;">${str}</div>`,
      '',
      {
        confirmButtonText: '确定复制',
        dangerouslyUseHTMLString: true,
      }
    )
    .then(res => {
      copy(str)
    })
    .catch(() => {})
}

function handleSelectiveAssembly(val) {
  let str = picCountOptions.find(item => item.value == val)
  return str ? str.label.substring(0, 2) : '-'
}

function handleContactAndIssue(contact, issue) {
  return (contact?.name || '-') + '/' + (issue?.name || '-')
}

function handleSelectionChange(val) {
  emits('selectionChange', val)
}

function handleOrderVideoRefundList(list, type) {
  let s = false
  if (type == '2' && list && list.length > 0) {
    list.find(item => {
      if (item.refundStatus == orderRefundStatusMap['退款成功'] && item.refundType == 1) {
        s = true
      }
    })
  }
  if (type == '1' && list && list.length > 0) {
    list.find(item => {
      if (item.refundStatus == orderRefundStatusMap['退款成功'] && item.refundType == 3) {
        s = true
      }
    })
  }
  return s
}

function routerNewWindow(path) {
  const { href } = router.resolve({ path })
  window.open(href, '_blank')
}

function handleAction(btn, row) {
  if (btn == '预选管理') {
    if (isRefund(row.orderVideoRefund)) return
  }
  if (btn == '重新获取产品图') {
    row.regainLoading = true
    crawlProductPic({ videoId: row.id })
      .then(res => {
        row.regain = false
        row.regainLoading = false
        ElMessage.success('已重新获取产品图片，请30s后刷新页面')
      })
      .catch(err => {
        row.regain = false
      })
    return
  }
  emits('action', btn, row)
}

let btnName = ''
let rowData = {}
function handleUpAction(btn, row) {
  btnName = btn
  rowData = row
}
function fileInputChange(file) {
  emits('uploadChange', file, btnName, rowData)
}
function checkContact() {
  ElMessageBox.alert('该商家还没有关联对接人，请先去关联~', '无法编辑订单', {
    autofocus: false,
    center: true,
    confirmButtonText: '我知道了',
  })
}

function handleCopyshootModelLink(row) {
  return new Promise((resolve, reject) => {
    if (!row.shootModel.id) {
      proxy.$modal.msgError('复制参数错误')
      return reject()
    }
    proxy.$modal.loading('获取中')
    createBackstageLink(row.shootModel.id)
      .then(res => {
        if (res.data) {
          let obj
          let pmId
          if (row.videoMatchOrderVO?.normalVideoMatchPreselectModelOrderVOS?.length) {
            pmId =
              row.videoMatchOrderVO.normalVideoMatchPreselectModelOrderVOS.find(
                item => item.model?.id == row.shootModel.id
              )?.id || undefined
          }
          if (row.videoCode && pmId) {
            obj = {
              vcode: row.videoCode,
              pmId,
              toType: 'p_detail',
            }
            return resolve(
              row.videoCode +
                '\t' +
                row.productChinese +
                '\t' +
                row.productEnglish +
                '\t\r\n' +
                res.data +
                `&to=${obj ? btoa(JSON.stringify(obj)) : ''}`
            )
          }
        }
        proxy.$modal.msgWarning('获取链接错误')
        reject()
      })
      .catch(() => {
        proxy.$modal.msgWarning('获取链接错误')
        reject()
      })
      .finally(() => proxy.$modal.closeLoading())
  })
}

function handleMouseEnter(e, id) {
  emits('hover', e.target, id, true)
}
function handleMouseLeave(e, id) {
  emits('hover', e.target, id, false)
}

function toTaskPage(type, row) {
  const path = `/task/workOrder?type=${type}&keyword=${row.videoCode}`
  window.open(path, '_blank')
}

const handleDiscount = (list, type) => {
  return list.find(item => item.type == type).amount
}
const handleShowDiscount = (list, type) => {
  return list && list.length > 0 ? list.some(item => item.type == type) : false
}

onMounted(() => {
  // window.onresize = () => {  //没啥用破坏vue的响应式
  //   let tableHeadHeight = document.querySelector('.head-box')?.offsetHeight || 31
  //   let temp = document.querySelector('.el-table__header-wrapper')
  //   if (temp) {
  //     temp.style.top = tableHeadHeight + 'px !important'
  //     temp.style.position = 'sticky'
  //     temp.style.zIndex = '5'
  //   }
  // }
})
</script>

<style scoped lang="scss">
:deep(.el-table) {
  overflow: visible;
}
:deep(.el-table__header-wrapper) {
  position: sticky; // 设置粘性定位
  top: 31px; // 距离顶部的位置
  z-index: 5;
}
.order-info-box {
  .head-box {
    font-size: 14px;
    padding: 6px 15px;
    background: #f3f3f6;
    position: sticky;
    top: 0;
    z-index: 999;
    .left-box {
      gap: 10px;
    }

    .head-label {
      span {
        font-weight: bold;
      }
    }
    .small-box-btn {
      :deep(.el-button--small) {
        --el-button-size: 20px;
      }
    }
  }
  :deep(.el-table--default) {
    .el-table__cell {
      padding: 0;
    }
  }
  .table-box {
    :deep(.el-table) {
      .el-table__header-wrapper {
        th {
          height: 30px !important;
        }
      }
      th {
        text-align: center;
      }

      .el-button + .el-button {
        margin: 0 3px;
      }

      .el-table__header-wrapper {
        .seltAllbtnDis {
          .cell {
            visibility: hidden;
          }
        }
      }
      .product-img-box {
        position: relative;

        .top-tag {
          z-index: 9;
          position: absolute;
          top: 2px;
          left: 1px;

          .el-tag + .el-tag {
            margin-left: 5px;
          }
        }

        .productPic-img {
          width: 100%;
          height: 100%;
          position: relative;

          .productPic-btn {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
          }
        }
      }
      .shoot-model-box {
        // padding-top: 8px;
        position: relative;
        overflow: hidden;

        .hint-box {
          z-index: 999;
          position: absolute;
          left: -6px;
          top: -2px;
          // overflow: hidden;
          width: 32px;
          height: 25px;

          .exchange {
            background: var(--el-color-danger);
            color: #fff;
            font-size: 13px;
            width: 60px;
            transform: translate(-17px, -3px) scale(0.8) rotateZ(-45deg);
          }
        }
      }
    }

    .preselect-model-corner-mark {
      width: 0;
      height: 0;
      border-top: 20px solid #70b603;
      border-left: 20px solid #70b603;
      border-bottom: 20px solid transparent;
      border-right: 20px solid transparent;
      position: absolute;
      top: 0;
      left: 0;

      &::after {
        content: '选定';
        color: #fff;
        position: absolute;
        top: -13px;
        left: -26px;
        width: 40px;
        font-size: 12px;
        line-height: 1;
        transform: rotateZ(315deg);
      }
    }

    .productLink {
      position: relative;
      padding-right: 5px;

      :deep(.el-link) {
        display: contents;

        .el-link__inner {
          display: inline;
        }
      }
    }

    .product-info-box {
      .btn {
        padding: 2px 4px;
        height: auto;
        font-size: 12px;
        margin-right: 2px;
      }
    }

    .btn-red-tip {
      &::after {
        content: '';
        display: block;
        width: 6px;
        height: 6px;
        background-color: red;
        border-radius: 50%;
        margin-left: 3px;
      }
    }

    .logistic-sign-time {
      width: 110px;
      text-align: center;
      margin: 3px auto 0;
      background: #d0ffff;
      border-radius: 10px;
      color: #333;
    }

    .task-tag-box {
      position: absolute;
      top: 0;
      right: 0;
      .tag-clip {
        color: var(--el-color-info);
        background-color: var(--el-fill-color-light);
        border: 1px solid var(--el-color-info-light-7);
        font-size: 12px;
        font-family: Arial;
        padding: 2px 8px;
        height: 18px;
        border-radius: 3px;
        display: flex;
        align-items: center;
      }
      .el-button {
        padding: 2px 8px;
        height: 18px;
        border: 1px solid var(--el-color-info-light-7);
        margin: 0 !important;
      }
    }
  }
  .urge-title {
    color: #fff;
    flex: 16px;
    background: #e5596a;
    border-radius: 50%;
    margin: 0 15px;
    padding: 2px 0;
  }
}
.discount-box {
  color: #d9001b;
  background-color: var(--el-color-danger-light-9);
  border: 1px solid #eb7685;
  border-radius: 6px;
  font-size: 12px;
  line-height: 18px;
  margin-left: 5px;
  &__left {
    background-color: #eb7685;
    color: #d9001b;
    padding: 0 5px;
    border-radius: 4px;
  }
  &__right {
    padding: 0 6px;
  }
}
</style>
