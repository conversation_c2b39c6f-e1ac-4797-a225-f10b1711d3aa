<template>
  <div class="model-edit-page" v-loading="pageLoading">
    <div class="detail-tag" @click="changeActiveName" v-if="disabled">
      <span class="left-btn" :class="{ active: avtiveName == 1 }">基础信息</span>
      <span class="right-btn" :class="{ active: avtiveName == 2 }">信息变更记录</span>
    </div>
    <el-card>
      <template #header v-if="avtiveName == 1">
        <div class="card-head">
          <!-- <span v-if="disabled">模特信息</span> -->
          <span>{{ titleHead }}</span>
        </div>
      </template>
      <template v-if="avtiveName == 1">
        <el-form
          v-loading="changeLoading"
          ref="formRef"
          :model="form"
          :rules="rules"
          :disabled="disabled"
          label-width="140px"
          style="max-width: 100%"
          @submit.prevent
          :validate-on-rule-change="false"
        >
          <div class="info-one">
            <div class="one-left">
              <div class="one-left-top">
                <el-form-item label="头像" prop="modelPicTemp" style="display: flex; flex-direction: column">
                  <div class="modelPicTempPic">
                    <div class="image-list" v-if="form.modelPicTemp?.length > 0">
                      <div
                        @click.stop="doDeleteUrl"
                        v-if="!disabled && isEditable.modelPicTemp"
                        class="image-modal flex-around"
                      >
                        <el-icon class="icon">
                          <Delete style="cursor: pointer; color: #ff3b30; font-size: 20px" />
                        </el-icon>
                      </div>
                      <el-image
                        ref="imgViewRef"
                        preview-teleported
                        :src="pathHead + form.modelPicTemp[0].picUrl + '!squarecompress'"
                        fit="fill"
                        class="image-item"
                        @click="showViewer([form.modelPicTemp[0].picUrl])"
                      ></el-image>
                    </div>
                    <div
                      class="image-upload"
                      v-if="form.modelPicTemp.length == 0 && !disabled && isEditable.modelPicTemp"
                      @click="doUploadImg('avatar')"
                    >
                      <el-icon size="28" color="#909399"><Plus /></el-icon>
                    </div>
                    <!-- <CropperUpload
                    v-model="form.modelPicTemp"
                    preview
                    :disabled="disabled"
                    :fixed-number-arr="[[3, 4]]"
                    :limit="1"
                    :bucketType="'model'"
                  /> -->
                  </div>
                  <div class="tips" style="margin-left: 90px">
                    请上传大小不超过5M，照片比例3:4（竖屏），格式为png/jpg、jpeg的文件
                    <!-- 1. 请上传比例为3:4，格式为
              <span style="color: #d9001b">png/jpg、jpeg</span>
              的文件; -->
                  </div>
                </el-form-item>
              </div>
              <el-form-item
                label="蜗牛照"
                prop="haveSnailPic"
                label-width="154px"
                label-position="top"
                style="margin-left: 90px"
              >
                <el-radio-group v-model="form.haveSnailPic" :disabled="!isEditable.haveSnailPic">
                  <el-radio-button :value="1">有</el-radio-button>
                  <el-radio-button :value="0">无</el-radio-button>
                </el-radio-group>
              </el-form-item>
            </div>
            <div class="one-center">
              <el-form-item label="模特姓名" prop="name">
                <el-input
                  :disabled="!isEditable.name"
                  v-model="form.name"
                  maxlength="16"
                  placeholder="请输入模特中文名称"
                  clearable
                />
              </el-form-item>
              <el-form-item label="性别" prop="sex">
                <el-radio-group v-model="form.sex" :disabled="!isEditable.sex">
                  <el-radio-button value="1">男</el-radio-button>
                  <el-radio-button value="0">女</el-radio-button>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="模特类型" prop="type">
                <el-radio-group v-model="form.type" :disabled="!isEditable.type">
                  <el-radio-button v-for="item in biz_model_type" :key="item.value" :value="item.value">
                    {{ item.label }}
                  </el-radio-button>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="模特年龄层" prop="ageGroup">
                <el-radio-group v-model="form.ageGroup" :disabled="!isEditable.ageGroup">
                  <el-radio-button v-for="item in biz_model_ageGroup" :key="item.value" :value="item.value">
                    {{ item.label }}
                  </el-radio-button>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="出生日期" prop="birthday">
                <el-date-picker
                  :disabled="!isEditable.birthday"
                  v-model="form.birthday"
                  clearable
                  type="date"
                  format="YYYY/M/D"
                  value-format="YYYY-MM-DD"
                  placeholder="请选择出生日期或输入日期 如：2000/1/1"
                  style="width: 100%"
                />
              </el-form-item>
              <el-form-item label="模特简介">
                <el-input
                  type="textarea"
                  :rows="3"
                  resize="none"
                  maxlength="150"
                  :disabled="!isEditable.about"
                  v-model="form.about"
                  placeholder="请输入模特简介"
                  clearable
                />
              </el-form-item>
            </div>
            <div class="one-right">
              <el-form-item label="国家" prop="nation">
                <el-radio-group v-model="form.nation" :disabled="nationDisabled || !isEditable.address">
                  <el-radio-button
                    v-for="item in biz_nation"
                    :key="item.value"
                    :value="item.value"
                    class="radioButton"
                  >
                    {{ item.label }}
                  </el-radio-button>
                </el-radio-group>
                <!-- <el-select v-model="form.nation" placeholder="请选择国家" clearable>
          <el-option v-for="item in biz_nation" :key="item.value" :label="item.label" :value="item.value" />
        </el-select> -->
              </el-form-item>
              <el-form-item label="擅长品类">
                <el-button
                  v-if="
                    (!form.tempSpecialtyCategory || form.tempSpecialtyCategory.length == 0) &&
                    !disabled &&
                    isEditable.specialtyCategory
                  "
                  link
                  type="primary"
                  @click="openSelectTagDialog(1)"
                >
                  + 请选择
                </el-button>
                <template v-if="form.tempSpecialtyCategory && form.tempSpecialtyCategory.length > 0">
                  <div
                    style="gap: 10px; display: flex; flex-wrap: wrap"
                    @click="openSelectTagDialog(1, form.tempSpecialtyCategory)"
                  >
                    <el-tag
                      :disable-transitions="true"
                      v-for="item in form.tempSpecialtyCategory"
                      :key="item.id"
                      effect="dark"
                    >
                      {{ item.name }}
                    </el-tag>
                    <!-- <el-tag v-for="item in form.specialtyCategory" :key="item" :type="item" effect="dark">
                      {{ modelCategoryList.find(data => data.id === item)?.name || item }}
                    </el-tag> -->
                    <el-button
                      v-if="!disabled && isEditable.specialtyCategory"
                      link
                      type="primary"
                      @click="openSelectTagDialog(1, form.tempSpecialtyCategory)"
                    >
                      重新选择
                    </el-button>
                  </div>
                </template>
                <!-- <div class="flex-start" style="gap: 10px; width: 100%">
                  <el-select
                    v-model="form.specialtyCategory"
                    multiple
                    :placeholder="dialogPlacehoder"
                    style="width: 100%"
                  >
                    <el-option
                      v-for="item in modelCategoryList"
                      :key="item.id"
                      :label="item.name"
                      :value="item.id"
                    />
                  </el-select>
                </div> -->
              </el-form-item>
              <el-form-item label="模特标签">
                <el-button
                  link
                  type="primary"
                  v-if="(!form.tempTags || form.tempTags.length == 0) && !disabled && isEditable.tags"
                  @click="openSelectTagDialog(2)"
                >
                  + 请选择
                </el-button>
                <template v-if="form.tempTags && form.tempTags.length > 0">
                  <div
                    style="gap: 10px; display: flex; flex-wrap: wrap"
                    @click="openSelectTagDialog(2, form.tempTags)"
                  >
                    <el-tag
                      :disable-transitions="true"
                      v-for="item in form.tempTags"
                      :key="item.id"
                      :type="item.id ? 'primary' : 'success'"
                      effect="dark"
                    >
                      {{ item.name }}
                    </el-tag>
                    <el-button
                      v-if="!disabled && isEditable.tags"
                      link
                      type="primary"
                      @click="openSelectTagDialog(2, form.tempTags)"
                    >
                      重新选择
                    </el-button>
                  </div>
                </template>

                <!-- <div class="flex-start" style="gap: 10px; width: 100%">
                  <el-tree-select
                    :reserve-keyword="false"
                    v-model="form.tags"
                    :data="modelTagsList"
                    multiple
                    :render-after-expand="false"
                    default-expand-all
                    show-checkbox
                    check-strictly
                    check-on-click-node
                    style="width: 100%"
                    filterable
                    :filter-node-method="filterTreeNode"
                    node-key="id"
                    :props="{
                      children: 'children',
                      label: 'name',
                      value: 'id',
                    }"
                  /> -->
                <!-- <el-tag
                v-for="(tag, index) in form.tags"
                :key="tag.name"
                :closable="!disabled"
                type="warning"
                effect="dark"
                @close="closeTags(form.tags, index)"
              >
                {{ tag.name }}
              </el-tag>
              <el-button
                v-btn
                v-if="!disabled"
                type="warning"
                plain
                icon="Plus"
                size="small"
                @click="addTags(form.tags, 2)"
              >
                添加
              </el-button> -->
                <!-- </div> -->
              </el-form-item>
              <el-form-item label="列表排序">
                <el-input-number
                  :disabled="!isEditable.sort"
                  title=""
                  style="width: 100%"
                  :min="0"
                  :max="99999"
                  :precision="0"
                  :step="1"
                  v-model="form.sort"
                  controls-position="right"
                  @keydown="channelInputLimit"
                />
                <div>数值越大排序越前</div>
              </el-form-item>
              <el-form-item label="展示开关">
                <el-radio-group v-model="form.isShow" :disabled="!isEditable.isShow">
                  <el-radio-button :value="1">展示</el-radio-button>
                  <el-radio-button :value="0">隐藏</el-radio-button>
                </el-radio-group>
                <div style="width: 100%">是否将该模特展示在商家端</div>
              </el-form-item>
            </div>
          </div>

          <div class="info-center">
            <div class="center-left">
              <Title>模特地址</Title>
              <el-form-item prop="recipient">
                <template #label>
                  <div class="label-text">
                    <div>收件人</div>
                    <div class="label-tips">{{ addressInfo.recipient }}</div>
                  </div>
                </template>
                <el-input
                  :disabled="!isEditable.address"
                  v-model="form.recipient"
                  placeholder="请输入收件人姓名"
                  clearable
                  style="width: 70%"
                  maxlength="32"
                />
              </el-form-item>
              <el-form-item prop="detailAddress" label-position="right">
                <template #label>
                  <div class="label-text">
                    <div>门牌号和街道名称</div>
                    <div class="label-tips">{{ addressInfo.detailAddress }}</div>
                  </div>
                </template>
                <el-input
                  :disabled="!isEditable.address"
                  v-model="form.detailAddress"
                  placeholder="请输入模特详细地址"
                  clearable
                  style="width: 70%"
                  maxlength="100"
                />
              </el-form-item>
              <el-form-item label="城市" prop="city">
                <template #label>
                  <div class="label-text">
                    <div>城市</div>
                    <div class="label-tips">{{ addressInfo.city }}</div>
                  </div>
                </template>
                <el-input
                  :disabled="!isEditable.address"
                  v-model="form.city"
                  placeholder="请输入模特所在城市"
                  clearable
                  style="width: 70%"
                  maxlength="100"
                />
              </el-form-item>
              <el-form-item
                prop="state"
                v-if="
                  form.nation == 2 ||
                  form.nation == 4 ||
                  form.nation == 6 ||
                  form.nation == 5 ||
                  form.nation == 3
                "
              >
                <template #label>
                  <div class="label-text">
                    <div v-if="form.nation == 6 || form.nation == 5 || form.nation == 3">省份/地区</div>
                    <div v-else>省</div>
                    <div class="label-tips">{{ addressInfo.province }}</div>
                  </div>
                </template>
                <el-input
                  :disabled="!isEditable.address"
                  v-model="form.state"
                  :placeholder="
                    form.nation == 6 || form.nation == 5 || form.nation == 3
                      ? '请输入模特所在省份/地区'
                      : '请输入模特所在省'
                  "
                  clearable
                  style="width: 70%"
                  maxlength="100"
                />
                <!-- <template #error>
                  4545
                </template> -->
              </el-form-item>
              <div v-if="form.nation == 7">
                <el-form-item prop="state">
                  <template #label>
                    <div class="label-text">
                      <div>州</div>
                      <div class="label-tips">{{ addressInfo.state }}</div>
                    </div>
                  </template>
                  <el-input
                    :disabled="!isEditable.address"
                    v-model="form.state"
                    placeholder="请输入模特所在州"
                    clearable
                    style="width: 70%"
                    maxlength="100"
                  />
                </el-form-item>
              </div>
              <div v-if="form.nation == 1">
                <el-form-item prop="state">
                  <template #label>
                    <div class="label-text">
                      <div>局部区域</div>
                      <div class="label-tips">{{ addressInfo.localArea }}</div>
                    </div>
                  </template>
                  <el-input
                    :disabled="!isEditable.address"
                    v-model="form.state"
                    placeholder="请输入模特所在区域"
                    clearable
                    style="width: 70%"
                    maxlength="100"
                  />
                </el-form-item>
              </div>

              <el-form-item label="邮编" prop="zipcode">
                <template #label>
                  <div class="label-text">
                    <div>邮编</div>
                    <div class="label-tips">{{ addressInfo.zipCode }}</div>
                  </div>
                </template>
                <el-input
                  :disabled="!isEditable.address"
                  v-model="form.zipcode"
                  placeholder="请输入所在城市邮编"
                  clearable
                  style="width: 70%"
                  maxlength="16"
                />
              </el-form-item>
              <el-form-item label="电话" prop="phone">
                <template #label>
                  <div class="label-text">
                    <div>电话</div>
                    <div class="label-tips">Number</div>
                  </div>
                </template>
                <el-input
                  :disabled="!isEditable.address"
                  v-model.trim="form.phone"
                  placeholder="请输入电话"
                  clearable
                  style="width: 70%"
                  maxlength="30"
                />
              </el-form-item>
            </div>
            <div class="center-right">
              <Title>详细信息</Title>
              <el-form-item label="适用平台" prop="platform">
                <el-checkbox-group
                  v-model="form.platform"
                  :disabled="platformDisabled || !isEditable.platform"
                >
                  <el-checkbox-button v-for="item in model_platform" :key="item.value" :value="item.value">
                    {{ item.label }}
                  </el-checkbox-button>
                </el-checkbox-group>
              </el-form-item>
              <el-form-item label="模特评分" prop="cooperationScore">
                <el-input-number
                  title=""
                  style="width: 70%"
                  :min="0"
                  :max="10"
                  :precision="1"
                  :step="0.1"
                  :disabled="!isEditable.cooperationScore"
                  v-model="form.cooperationScore"
                  controls-position="right"
                  placeholder="请输入模特评分"
                  @keydown="channelInputLimit"
                ></el-input-number>
              </el-form-item>
              <!-- <el-form-item label="模特等级" prop="cooperation">
                <el-radio-group v-model="form.cooperation" :disabled="!isEditCooperation">
                  <el-radio-button
                    v-for="item in biz_model_cooperation"
                    :key="item.value"
                    :value="item.value"
                  >
                    {{ item.label }}
                  </el-radio-button>
                </el-radio-group>
              </el-form-item> -->
              <el-form-item label="英文客服" prop="persons">
                <!-- <SelectLoad
          v-model="form.persons"
          :request="listUser"
          :requestCallback="listUserCallback"
          keyValue="userId"
          keyLabel="userName"
          keyWord="userName"
        /> -->
                <el-select
                  v-model="form.persons"
                  filterable
                  :disabled="!isEditable.persons"
                  placeholder="请选择对接人员"
                  clearable
                  style="width: 70%"
                >
                  <el-option
                    v-for="item in contactList"
                    :key="item.userId"
                    :label="item.userName"
                    :value="item.userId"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="开发人" prop="developerId">
                <el-select
                  v-model="form.developerId"
                  filterable
                  :disabled="!isEditable.developerId"
                  placeholder="请选择开发人"
                  clearable
                  style="width: 70%"
                >
                  <el-option
                    v-for="item in contactList"
                    :key="item.userId"
                    :label="item.userName"
                    :value="item.userId"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="待完成最高接受量" prop="acceptability">
                <el-input-number
                  :disabled="!isEditable.acceptability"
                  title=""
                  v-model="form.acceptability"
                  :min="0"
                  controls-position="right"
                  style="width: 70%"
                  :max="99999"
                >
                  <template #decrease-icon>
                    <el-icon><Minus /></el-icon>
                  </template>
                  <template #increase-icon>
                    <el-icon><Plus /></el-icon>
                  </template>
                </el-input-number>
              </el-form-item>
              <el-form-item label="模特佣金" prop="commission">
                <div class="flex-column" style="align-items: flex-start">
                  <el-radio-group v-model="form.commissionUnit" :disabled="!isEditable.commission">
                    <el-radio-button v-for="item in bizCommissionUnit" :key="item.value" :value="item.value">
                      {{ item.label }}
                    </el-radio-button>
                  </el-radio-group>
                  <el-input-number
                    :disabled="!isEditable.commission"
                    v-model="form.commission"
                    :min="0"
                    :precision="2"
                    title=""
                    controls-position="right"
                    style="margin-top: 10px"
                  >
                    <template #decrease-icon>
                      <el-icon><Minus /></el-icon>
                    </template>
                    <template #increase-icon>
                      <el-icon><Plus /></el-icon>
                    </template>
                  </el-input-number>
                </div>
              </el-form-item>
            </div>
          </div>

          <div class="info-footer">
            <div class="footer-left">
              <Title>案例视频</Title>
              <div style="margin: 0 10px 0 80px">
                <div
                  class="tips"
                  style="
                    background: rgb(249, 227, 197);
                    width: 550px;
                    padding: 10px 20px;
                    border-radius: 10px;
                    color: #666;
                    margin: 10px 0;
                  "
                >
                  1. 请完整填入视频可以访问的链接;
                  <br />
                  2.
                  每个视频链接需要上传1张封面图片，用于模特库展示；（图片大小不超过5M，格式为png/jpg、jpeg的文件）
                  <br />
                  3. 亚马逊和Tik Tok最多各传50个视频
                </div>
                <div>
                  <div class="footer-text">亚马逊案例视频</div>
                  <div style="display: flex; flex-wrap: wrap; max-width: 600px">
                    <DragUploadImage
                      :urlList="form.amazonVideo"
                      :type="'amazon'"
                      @change="editSelectImgInfo"
                      :isShowIcon="disabled"
                      :disabledEdit="isEditable.caseVideo"
                      @openVideo="handleOpenVideo"
                      :showVideo="disabled"
                      @update:urlList="changeUrlList"
                    />
                    <!-- <UploadImage
                      :urlList="form.amazonVideo"
                      :type="'amazon'"
                      @change="editSelectImgInfo"
                      :isShowIcon="disabled"
                      :disabledEdit="isEditCase"
                      @openVideo="handleOpenVideo"
                      :showVideo="disabled"
                    /> -->
                    <div
                      v-if="form.amazonVideo && form.amazonVideo.length < 50 && !disabled && isEditable.caseVideo"
                      class="image-upload"
                      @click="doShowVideoDialog('amazon', 'add')"
                    >
                      <el-icon size="28" color="#909399"><Plus /></el-icon>
                    </div>
                  </div>
                </div>
                <div>
                  <div class="footer-text">Tik Tok案例视频</div>
                  <div style="display: flex; flex-wrap: wrap; max-width: 600px">
                    <DragUploadImage
                      :urlList="form.tiktokVideo"
                      :type="'tiktok'"
                      @change="editSelectImgInfo"
                      :isShowIcon="disabled"
                      :disabledEdit="isEditable.caseVideo"
                      @openVideo="handleOpenVideo"
                      :showVideo="disabled"
                      @update:urlList="changeTiktokUrlList"
                    />
                    <!-- <UploadImage
                      :urlList="form.tiktokVideo"
                      :type="'tiktok'"
                      @change="editSelectImgInfo"
                      :isShowIcon="disabled"
                      :disabledEdit="isEditCase"
                      @openVideo="handleOpenVideo"
                      :showVideo="disabled"
                    /> -->
                    <div
                      v-if="form.tiktokVideo && form.tiktokVideo.length < 50 && !disabled && isEditable.caseVideo"
                      class="image-upload"
                      @click="doShowVideoDialog('tiktok', 'add')"
                    >
                      <el-icon size="28" color="#909399"><Plus /></el-icon>
                    </div>
                  </div>
                </div>
                <!-- <div class="flex-start" style="margin-bottom: 20px; gap: 10px">
              亚马逊平台案例视频
              <el-button
                v-btn
                v-if="!disabled && form.amazonVideo.length < 10"
                type="warning"
                plain
                icon="Plus"
                @click="addVedeoList('amazonVideo')"
              />
            </div>
            <template v-for="(item, i) in form.amazonVideo" :key="item">
              <el-form-item
                :label="'视频' + (i + 1)"
                :prop="'amazonVideo.' + i + '.videoUrl'"
                :rules="rules.videoList"
              >
                <div class="flex-start videoUpItem">
                  <el-input v-model="form.amazonVideo[i].name" placeholder="请输入视频名称" clearable />
                  <el-icon
                    v-if="!disabled"
                    :size="24"
                    color="#ff5722"
                    @click="delVedeoList('amazonVideo', i)"
                  >
                    <Delete />
                  </el-icon>
                </div>
                <el-input v-model="form.amazonVideo[i].videoUrl" placeholder="请输入视频链接" clearable />
              </el-form-item>
              <UpVideoCover
                v-model:file="form.amazonVideo[i].pic"
                :disabled="disabled"
                style="margin: 10px 0 10px 140px"
                @change="videoUploadChange('amazonVideo.' + i + '.videoUrl')"
              />
            </template> -->
                <!-- <div class="flex-start" style="margin: 20px 0; gap: 10px">
              TIKTOK平台案例视频
              <el-button
                v-btn
                v-if="!disabled && form.tiktokVideo.length < 10"
                type="warning"
                plain
                icon="Plus"
                @click="addVedeoList('tiktokVideo')"
              />
            </div>
            <template v-for="(item, i) in form.tiktokVideo" :key="item">
              <el-form-item
                :label="'视频' + (i + 1)"
                :prop="'tiktokVideo.' + i + '.videoUrl'"
                :rules="rules.videoList"
              >
                <div class="flex-start videoUpItem">
                  <el-input v-model="form.tiktokVideo[i].name" placeholder="请输入视频名称" clearable />
                  <el-icon
                    v-if="!disabled"
                    :size="24"
                    color="#ff5722"
                    @click="delVedeoList('tiktokVideo', i)"
                  >
                    <Delete />
                  </el-icon>
                </div>
                <el-input v-model="form.tiktokVideo[i].videoUrl" placeholder="请输入视频链接" clearable />
              </el-form-item>
              <UpVideoCover
                v-model:file="form.tiktokVideo[i].pic"
                :disabled="disabled"
                style="margin: 10px 0 10px 140px"
                @change="videoUploadChange('tiktokVideo.' + i + '.videoUrl')"
              />
            </template> -->
              </div>
            </div>
            <div class="footer-right">
              <Title>
                <div class="flex-center">
                  <!-- <div style="color: var(--el-color-danger); margin-right: 3px">*</div> -->
                  <div>生活照</div>
                </div>
              </Title>
              <div
                class="tips"
                style="
                  background: rgb(249, 227, 197);
                  width: 550px;
                  padding: 30px 20px;
                  border-radius: 10px;
                  color: #666;
                  margin: 10px 0 30px;
                "
              >
                1.请上传图片大小不超过5M，比例为正方形，格式为
                <span style="color: #d9001b">png/jpg、jpeg</span>
                的文件;
                <br />
                2. 最多可以传30张
                <!-- <br /> -->
                <!-- 3. 第一张会作为列表缩略图 -->
              </div>
              <el-form-item prop="lifePhoto" style="margin-left: -110px">
                <div class="image-list" v-for="(item, i) in form.lifePhoto" :key="item.id">
                  <div
                    @click.stop="doDeletePicUrl(i)"
                    v-if="!disabled && isEditable.lifePhoto"
                    class="image-modal flex-around"
                  >
                    <!-- <el-icon class="icon"><CircleCloseFilled /></el-icon> -->
                    <el-icon size="20" color="#ff3b30">
                      <Delete style="cursor: pointer" />
                    </el-icon>
                  </div>
                  <el-image
                    ref="imgViewRef"
                    preview-teleported
                    :src="pathHead + item.picUrl + '!squarecompress'"
                    fit="fill"
                    class="image-item"
                    @click="showPic(form.lifePhoto, i)"
                  ></el-image>
                </div>

                <div
                  class="image-upload"
                  v-if="!disabled && form.lifePhoto.length < 30 && isEditable.lifePhoto"
                  @click="doUploadImg('life')"
                >
                  <el-icon size="28" color="#909399"><Plus /></el-icon>
                </div>
                <!-- <CropperUpload
                  v-model="form.lifePhoto"
                  preview
                  :disabled="disabled"
                  :fixed-number-arr="[[4, 4]]"
                  :limit="limit"
                  :bucketType="'model'"
                /> -->
              </el-form-item>
            </div>
          </div>

          <el-form-item style="margin: 30px; margin-left: 30vw" v-if="!readonly">
            <el-button v-btn @click="goBackPage" :disabled="false">取 消</el-button>
            <el-button v-btn type="primary" native-type="submit" @click="onSubmit" :loading="loading">
              保 存
            </el-button>
          </el-form-item>
        </el-form>
        <el-button v-btn v-if="readonly" @click="goBackPage" :disabled="false" style="margin-left: 35vw">
          返 回
        </el-button>
      </template>
      <template v-else>
        <el-table :data="tableData" stripe :loading="tableLoading">
          <template #empty>
            <el-empty description="暂无数据" :image-size="80"></el-empty>
          </template>
          <el-table-column prop="operateTime" label="操作时间" />
          <el-table-column prop="operateUserName" label="操作者" />
          <el-table-column prop="operateType" label="操作类型">
            <template v-slot="{ row }">
              {{ operateTypeList.find(item => item.value == row.operateType)?.label }}
            </template>
          </el-table-column>
          <el-table-column fiexd="right" prop="operateDetail" label="操作详情" v-loading="tableLoading">
            <template v-slot="{ row }">
              <div>
                <div>{{ row.operateDetail }}</div>
                <div v-if="row.operateExplain">说明：{{ row.operateExplain }}</div>
              </div>
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-show="total > 0"
          layout="total, prev, pager, next, jumper"
          :total="total"
          v-model:page="pageNum"
          v-model:limit="pageSize"
        />
      </template>
    </el-card>

    <!-- 添加视频案例 -->
    <el-dialog
      v-model="dialogFormVisible"
      :title="dialogVideoTitle"
      width="500"
      append-to-body
      align-center
      :close-on-click-modal="false"
      @close="closeVideoDialog"
    >
      <div class="dialog-content">{{ dialogType }}</div>
      <el-form :model="dialogVideoForm" label-width="auto" ref="dialogVideoFormRef" :rules="dialogRules">
        <el-form-item label="视频名称" prop="name">
          <el-input v-model="dialogVideoForm.name" placeholder="请输入视频名称" clearable maxlength="32" />
        </el-form-item>
        <el-form-item label="视频链接" prop="videoUrl">
          <el-input
            v-model="dialogVideoForm.videoUrl"
            placeholder="请输入视频链接"
            clearable
            maxlength="1000"
          />
        </el-form-item>
        <el-form-item label="封面图片" prop="picUrl">
          <div v-if="dialogVideoForm.picUrl">
            <!--  -->
            <div class="upload-img">
              <el-icon class="img-icon" color="#ff3b30" @click="deleteImg"><Delete /></el-icon>
              <el-image
                style="width: 80px; height: 80px"
                :src="$picUrl + dialogVideoForm.picUrl + '!1x1compress'"
              >
                <!-- <template #error>
                  <div
                    class="img-error"
                  >
                    暂未上传
                  </div>
                </template> -->
              </el-image>
            </div>
            <!-- <el-upload
              v-else
              action=""
              list-type="picture"
              :auto-upload="false"
              :show-file-list="false"
              :on-change="fileInputChange"
            >
              <el-button v-btn link type="primary">上传图片</el-button>
            </el-upload> -->
            <!-- <el-button v-else link type="primary" @click="doUploadImg('video')">上传图片</el-button> -->
          </div>
          <PasteUpload
            v-else
            :limit="1"
            :bucketType="'model'"
            style="width: 100%"
            @success="pasteUploadChange"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer flex-center">
          <el-button round v-btn @click="closeVideoDialog">取消</el-button>
          <el-button round v-btn type="primary" @click="confirmAddVedio">确定</el-button>
        </div>
      </template>
    </el-dialog>
    <DragUploadDialog
      ref="DragUploadDialogRef"
      :title="uploadTitle"
      :limit="uploadLimit"
      :before-success="uploadInvoice"
      :bucketType="'model'"
    />
    <CropperDialog
      ref="CropperDialogRef"
      :img="imgFile"
      :fixed-number-arr="[[4, 4]]"
      :previews-width="180"
      :is-show-action="false"
      @confirm="onConfirmCropper"
    />
    <DialogVideo ref="dialogVideoRef" :videoSrc="videoSrc" @close="videoSrc = ''" />
    <SelectTagDialog ref="selectTagDialogRef" @success="addTagSuccess" />
  </div>
</template>

<script setup name="handleModel">
import Title from '@/components/Public/title'
import UpVideoCover from './components/uploadCover.vue'
import SelectLoad from '@/components/Select/SelectLoad.vue'
import SelectTagDialog from '@/views/model/modelManage/components/selectTagDialog.vue'
import DialogVideo from '@/components/Dialog/video'
import CropperUpload from '@/components/FileUpload/cropperUpload'
import CropperDialog from '@/components/Cropper/cropperDialog'
import { uploadCloudFile } from '@/api/index'
import UploadImage from '@/views/model/modelManage/components/uploadImage.vue'
import DragUploadImage from '@/views/model/modelManage/components/dragUploadImage.vue'
import PasteUpload from '@/components/FileUpload/pasteUpload.vue'
import { checkPermi } from '@/utils/permission'

import {
  addModel,
  editModel,
  getModelDetail,
  modelCategorySelect,
  modelCategorySelectRank,
  getModelChangeRecord,
} from '@/api/model/model'
import { ElMessage } from 'element-plus'
import { listUser } from '@/api/system/user'
import { bizCommissionUnit, addressInfoList } from '@/utils/dict'
import { http_reg, number_d_reg } from '@/utils/RegExp'
import { ref, watch } from 'vue'
import { useViewer } from '@/hooks/useViewer'
const { showViewer } = useViewer()

const { proxy } = getCurrentInstance()
const route = useRoute()
const pathHead = proxy.$picUrl
// console.log(route);

const { biz_model_type, biz_nation, biz_model_cooperation, biz_model_ageGroup } = proxy.useDict(
  'biz_model_type',
  'biz_nation',
  'biz_model_cooperation',
  'biz_model_ageGroup'
)

const pageLoading = ref(false)
const readonly = ref(false)
const disabled = ref(false)
const loading = ref(false)
const dialogVideoRef = ref(null)
const videoSrc = ref('')
const DragUploadDialogRef = ref()
const CropperDialogRef = ref()
const dialogVideoFormRef = ref()
const avtiveName = ref('1')
const formRef = ref()
const model_platform = ref([
  { label: '亚马逊', value: '0' },
  { label: 'TikTok', value: '1' },
  { label: '其他', value: '2' },
  { label: 'APP/解说类', value: '3' },
])

const operateTypeList = [
  { label: '新增模特', value: '1' },
  { label: '修改模特信息', value: '2' },
  { label: '变更状态', value: '3' },
  { label: '变更置顶', value: '4' },
  { label: '修改模特排序', value: '5' },
  { label: '新增家庭成员', value: '6' },
  { label: '删除家庭成员', value: '7' },
]

const uploadTitle = ref('上传头像')
const uploadLimit = ref(1)
const uploadListTypes = ref('')

const isEditCheck = (isCheck, val) => {
  let arr = [
    // 基础信息
    'modelPicTemp',
    'haveSnailPic',
    'name',
    'sex',
    'type',
    'ageGroup',
    'birthday',
    'about',
    // 国家/模特地址
    'address',
    // 合作信息
    'specialtyCategory',
    'tags',
    'sort',
    'isShow',
    // 详细信息
    'platform',
    'cooperationScore',
    'persons',
    'developerId',
    'acceptability',
    'commission',
    // 案例视频
    'caseVideo',
    // 生活照
    'lifePhoto',
  ]
  let obj = {}
  arr.forEach(key => {
    obj[key] = isCheck ? checkPermi([`model:manage:edit-${key}`]) : val
  })
  return obj
}
// 是否可编辑模特信息
const isEditable = computed(() => {
  if (route.name == 'HandleEditModel') {
    return isEditCheck(true, false)
  } else if (route.name == 'HandleAddModel') {
    return isEditCheck(false, true)
  } else {
    return isEditCheck(false, false)
  }
})

const total = ref(1)
const pageNum = ref(1)
const pageSize = ref(10)
const tableData = ref([])
const form = ref({
  type: '1',
  name: '',
  sort: 0,
  sex: '1',
  haveSnailPic: '',
  ageGroup: '',
  birthday: '',
  nation: '7',
  recipient: '',
  city: '',
  state: '',
  zipcode: '',
  phone: null,
  detailAddress: '',
  localArea: '',
  province: '',
  lifePhoto: [],
  modelPicTemp: [],
  modelPic: {},
  specialtyCategory: [],
  about: '',
  isShow: 1,
  tags: [],
  travels: [
    {
      date: [],
      remark: '',
    },
  ],
  platform: ['0'],
  // cooperation: '0',
  cooperationScore: null,
  persons: '',
  developerId: '',
  acceptability: null,
  commissionUnit: 'USD',
  commission: null,
  amazonVideo: [],
  tiktokVideo: [],
})
const contactList = ref([])

const imgFile = ref({})

function initVideoListItem() {
  return {
    name: '',
    pic: {
      name: '',
      url: '',
    },
    videoUrl: '',
  }
}

function handleOpenVideo(src) {
  videoSrc.value = src
  dialogVideoRef.value?.open()
}

function changeUrlList(list) {
  console.log(list, 1)
  form.value.amazonVideo = list
}

function changeTiktokUrlList(list) {
  console.log(list, 2)
  form.value.tiktokVideo = list
}

function disposeUrl(url) {
  return url.startsWith(pathHead) ? url : pathHead + url
}

// 上传裁剪产品图
async function onConfirmCropper(img) {
  // console.log(img, uploadRowData);
  proxy.$modal.loading('正在上传中...')
  try {
    const formData = new FormData()
    formData.append('file', img.raw)
    let upres = await uploadCloudFile(formData, 'order')
    dialogVideoForm.value.picUrl = upres.data.picUrl
    dialogVideoForm.value.id = upres.data.picUrl
    // form.value.productPic = upres.data.picUrl
    // productLinkImg.value = upres.data.picUrl
    proxy.$modal.closeLoading()
    proxy.$modal.msgSuccess('上传成功！')
  } catch (e) {
    proxy.$modal.closeLoading()
  }
}

//选择模特标签弹窗
const selectTagDialogRef = ref(null)
function openSelectTagDialog(type, ids = []) {
  if (
    (type == 1 && !isEditable.value.specialtyCategory) ||
    (type == 2 && !isEditable.value.tags) ||
    disabled.value
  )
    return
  selectTagDialogRef.value.open(type, ids)
}

function addTagSuccess(type, list) {
  if (type == 1) {
    form.value.tempSpecialtyCategory = list || []
    form.value.specialtyCategory = list.map(item => item.id)
  } else if (type == 2) {
    form.value.tempTags = list || []
    form.value.tags = list.map(item => {
      if (item.id) return { id: item.id }
      else return { name: item.name }
    })
  }
}

const titleHead = ref('新增模特')

function init() {
  titleHead.value =
    route.name == 'HandleAddModel' ? '新增模特' : route.name == 'HandleEditModel' ? '编辑模特' : '模特信息'
  if (route.params.readonly == '1') {
    readonly.value = true
    disabled.value = true
    route.meta.title = '模特详情'
  } else {
    getModelCategorySelect()
    getModelTagsSelect()
  }
  if (route.params.modelId) {
    pageLoading.value = true
    getModelDetail(route.params.modelId)
      .then(res => {
        let lifePhoto = []
        let modelPicTemp = []
        let modelPic = {}
        let amazonVideo = []
        let tiktokVideo = []
        if (res.data.lifePhoto && res.data.lifePhoto.length) {
          lifePhoto = res.data.lifePhoto.map(item => ({
            // url: disposeUrl(item.picUrl),
            id: item,
            url: pathHead + item,
            picUrl: item,
          }))
        }
        // if (res.data.modelPic && res.data.modelPic.length) {
        //   modelPic = res.data.modelPic.map(item => ({
        //     ...item,
        //     url: item.picUrl,
        //   }))
        // }
        // if (res.data.modelPic && res.data.modelPic.picUrl) {
        //   modelPicTemp = [...res.data.modelPic]
        // }
        const data = {
          url: pathHead + res.data.modelPic,
          picUrl: res.data.modelPic,
        }
        modelPicTemp.push(data)
        // modelPicTemp[0].picUrl = res.data.picUrl
        modelPic = res.data.modelPic
        if (res.data.amazonVideo && res.data.amazonVideo.length) {
          // res.data.amazonVideo.forEach((item, index) => {
          //   item.id = index + Math.random().toString(36).substring(2)
          // })
          res.data.amazonVideo.sort((a, b) => b.sort - a.sort)
          amazonVideo = res.data.amazonVideo.map(item => ({
            name: item.name,
            url: item.picUri,
            picUrl: item.picUri,
            videoUrl: item.videoUrl,
            id: item.id,
          }))
        }

        if (res.data.tiktokVideo && res.data.tiktokVideo.length) {
          // res.data.tiktokVideo.forEach((item, index) => {
          //   item.id = index + Math.random().toString(36).substring(2)
          // })
          res.data.tiktokVideo.sort((a, b) => b.sort - a.sort)
          tiktokVideo = res.data.tiktokVideo.map(item => ({
            name: item.name,
            url: item.picUri,
            picUrl: item.picUri,
            videoUrl: item.videoUrl,
            id: item.id,
          }))
        }
        if (route.name == 'HandleAddModel') {
          const resData = {
            nation: res.data.nation + '',
            addressInfo: res.data.addressInfo,
            platform: res.data.platform.split(','),
            persons: res.data.persons ? res.data.persons.map(item => item.id)[0] : '',
            developerId: res.data.developerUser?.id || '',
            commissionUnit: res.data.commissionUnit,
            commission: res.data.commission,
            recipient: res.data.recipient || '',
            city: res.data.city || '',
            state: res.data.state || '',
            zipcode: res.data.zipcode || '',
            phone: res.data.phone || '',
            localArea: res.data.localArea || '',
            province: res.data.province || '',
            detailAddress: res.data.detailAddress || '',
          }
          form.value = { ...form.value, ...resData }
        } else {
          form.value = {
            ...res.data,
            sex: res.data.sex + '',
            haveSnailPic: res.data.haveSnailPic === null ? '' : res.data.haveSnailPic,
            age: res.data.age + '',
            ageGroup: res.data.ageGroup + '',
            nation: res.data.nation + '',
            type: res.data.type + '',
            // cooperation: res.data.cooperation + '',
            cooperationScore:
              res.data.cooperationScore || res.data.cooperationScore === 0 ? res.data.cooperationScore : null,
            sort: res.data.sort || 0,
            tempTags: res.data.tags || [],
            tempSpecialtyCategory: res.data.specialtyCategory || [],
            tags: res.data.tags || [],
            specialtyCategory: res.data.specialtyCategory || [],
            tags:
              route.params.readonly == '1'
                ? res.data.tags.map(item => item.name) || []
                : res.data.tags.map(item => item.id) || [],
            specialtyCategory:
              route.params.readonly == '1'
                ? res.data.specialtyCategory.map(item => item.name) || []
                : res.data.specialtyCategory.map(item => item.id) || [],

            platform: res.data.platform.split(','),
            persons: res.data.persons ? res.data.persons.map(item => item.id)[0] : '',
            developerId: res.data.developerUser?.id || '',
            travels:
              res.data.travels && res.data.travels.length
                ? res.data.travels.map(t => ({
                    remark: t.remark,
                    date: [t.startTime, t.endTime],
                  }))
                : [
                    {
                      date: [],
                      remark: '',
                    },
                  ],
            lifePhoto,
            modelPicTemp,
            modelPic,
            amazonVideo: amazonVideo || [],
            tiktokVideo: tiktokVideo || [],
          }
        }
        if (route.name == 'HandleEditModel') {
          form.value.oldAmazonVideo = res.data.amazonVideo || []
          form.value.oldTiktokVideo = res.data.tiktokVideo || []
          form.value.oldSpecialtyCategory = res.data.specialtyCategory || []
          form.value.oldTags = res.data.tags || []
        }
      })
      .catch(err => {
        console.log(err)
        proxy.$tab.closeOpenPage({ path: '/mdoel/model/modelManage' })
      })
      .finally(() => (pageLoading.value = false))
  }
  // 对接关联人员
  listUser().then(res => {
    if (res.data) {
      contactList.value = res.data.map(item => ({
        ...item,
        userName: item.userName + `(${item.userId})`,
      }))
    }
  })
}

function checkPhone(rule, value, callback) {
  if (!number_d_reg.test(value) || value == '') {
    return callback(new Error('请输入正确的电话号码'))
  }
  return callback()
}

function checkVideoList(rule, value, callback) {
  // console.log(rule, value);
  let field = rule.field.split('.')
  let data = form.value[field[0]][field[1]]
  // console.log(data);
  if (data.videoUrl && !data.pic.url) {
    if (!checkLinkSuffix(data.videoUrl)) {
      return callback(new Error('视频链接格式有误！'))
    }
    return callback(new Error('视频链接需要上传封面！'))
  }
  if (!data.videoUrl && data.pic.url) {
    return callback(new Error('请填写视频链接！'))
  }
  return callback()
}

function checkLinkSuffix(url) {
  return http_reg.test(url)
}
function checkVideoUrl(rule, value, callback) {
  if (value) {
    if (!checkLinkSuffix(value)) {
      return callback(new Error('视频链接格式有误！'))
    }
  }
  if (!value && !dialogVideoForm.value.videoUrl) {
    return callback(new Error('请填写视频链接！'))
  }
  return callback()
}

const changeLoading = ref(false)

//详情切换tab
function changeActiveName(event) {
  avtiveName.value = event.target.innerText == '基础信息' ? '1' : '2'
  if (avtiveName.value == 2) {
    getModelChange()
  }
  // changeLoading.value = true
}
const channelInputLimit = e => {
  const key = e.key
  const notAllowList = ['e', '+', '-']
  if (notAllowList.includes(key)) {
    e.returnValue = false
    return false
  }
  return true
}
const tableLoading = ref(false)
//模特变更记录
function getModelChange() {
  tableLoading.value = true
  getModelChangeRecord(route.params.modelId)
    .then(res => {
      tableData.value = res.data || []
    })
    .finally(() => (tableLoading.value = false))
}

const rules = ref({
  type: [{ required: true, message: '请选择模特类型', trigger: 'change' }],
  name: [{ required: true, message: '请输入模特中文名称', trigger: 'blur' }],
  sex: [{ required: true, message: '请选择模特性别', trigger: 'change' }],
  haveSnailPic: [{ required: true, message: '请选择蜗牛照', trigger: 'change' }],
  ageGroup: [{ required: true, message: '请选择模特年龄层', trigger: 'change' }],
  birthday: [{ required: false, message: '请选择出生日期', trigger: 'change' }],
  nation: [{ required: true, message: '请选择国家', trigger: 'change' }],
  recipient: [{ required: true, message: '请输入收件人姓名', trigger: 'blur' }],
  city: [{ required: true, message: '请输入模特所在城市', trigger: 'blur' }],
  state: [{ required: true, message: '请输入模特所在州', trigger: 'blur' }],
  zipcode: [{ required: true, message: '请输入所在城市邮编', trigger: 'blur' }],
  phone: [{ required: true, validator: checkPhone, trigger: 'change' }],
  localArea: [{ required: true, message: '请输入所在区域', trigger: 'blur' }],
  province: [{ required: true, message: '请输入所在省份', trigger: 'blur' }],
  detailAddress: [{ required: true, message: '请输入模特详细地址', trigger: 'blur' }],
  modelPicTemp: [{ required: true, message: '请上传模特头图', trigger: 'blur' }],
  // lifePhoto: [{ required: true, message: '请上传生活场景照', trigger: 'blur' }],
  platform: [{ required: true, message: '请选择合作平台', trigger: 'blur' }],
  // cooperation: [{ required: true, message: '请选择模特等级', trigger: 'blur' }],
  cooperationScore: [{ required: true, message: '请输入模特评分', trigger: ['blur', 'change'] }],
  persons: [{ required: true, message: '请选择关联英文部客服', trigger: 'blur' }],
  developerId: [{ required: true, message: '请选择开发人', trigger: 'blur' }],
  acceptability: [{ required: true, message: '请输入最高接受量', trigger: 'blur' }],
  commission: [{ required: true, message: '请输入模特佣金', trigger: 'blur' }],
  videoList: [{ required: false, validator: checkVideoList, trigger: 'change' }],
})
const dialogRules = {
  name: [{ required: true, message: '请输入视频名称', trigger: 'blur' }],
  videoUrl: [{ required: true, validator: checkVideoUrl, trigger: 'blur' }],
  picUrl: [{ required: true, message: '请上传封面图片', trigger: 'blur' }],
}

const addressInfo = ref(addressInfoList[6])

watch(
  () => form.value.nation,
  (newVal, oldVal) => {
    if (newVal == '7') {
      rules.value.state[0].message = '请输入模特所在州'
    } else if (newVal == '3' || newVal == '5' || newVal == '6') {
      rules.value.state[0].message = '请输入模特所在省份/地区'
    } else if (newVal == '2' || newVal == '4') {
      rules.value.state[0].message = '请输入模特所在省'
    } else if (newVal == '1') {
      rules.value.state[0].message = '请输入模特所在区域'
    }
    addressInfo.value = addressInfoList.find(item => item.value == newVal)
  }
)

const platformDisabled = computed(() => {
  if (form.value.type == '0') {
    console.log('platformDisabled 0')
    form.value.platform = ['0']
    return true
  }
  return disabled.value
})
const nationDisabled = computed(() => {
  if (form.value.type == '0') {
    form.value.nation = '7'
    return true
  }
  return disabled.value
})

function videoUploadChange(prop) {
  // console.log(prop);
  nextTick(() => {
    formRef.value.validateField(prop)
  })
}

const shortcuts = [
  {
    text: '最近一周',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setDate(start.getDate() - 7)
      return [start, end]
    },
  },
  {
    text: '最近一个月',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setMonth(start.getMonth() - 1)
      return [start, end]
    },
  },
  {
    text: '最近三个月',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setMonth(start.getMonth() - 3)
      return [start, end]
    },
  },
]

const limit = ref(30)

function listUserCallback(res) {
  return res.rows
}

const modelCategoryList = ref([])
const modelTagsList = ref([])
// 模特擅长品类下拉
function getModelCategorySelect() {
  modelCategorySelectRank({ rank: 1, categoryId: 1008 }).then(res => {
    if (res.code == 200) {
      modelCategoryList.value = res.data
    }
  })
}
// 模特标签下拉
function getModelTagsSelect() {
  modelCategorySelect({ rank: 2, status: 0, categoryId: 1009 }).then(res => {
    if (res.code == 200) {
      modelTagsList.value = res.data
    }
  })
}

//弹窗添加视频
const dialogVideoForm = ref({
  name: '',
  videoUrl: '',
  picUrl: '',
  id: '',
})
const dialogType = ref('')
const dialogVideoTitle = ref('新增视频案例')
const videoType = ref('add')
//新增视频按钮
function doShowVideoDialog(platform, type) {
  if (type == 'edit' && !isEditable.value.caseVideo) return ElMessage.warning('暂无权限')
  dialogType.value = platform == 'amazon' ? 'amazon' : 'Tik Tok'
  dialogVideoTitle.value = type == 'add' ? '新增视频案例' : '编辑视频案例'
  videoType.value = type
  dialogFormVisible.value = true
}
//上传视频封面图
function fileInputChange(file) {
  imgFile.value = file
  CropperDialogRef.value.open()
}

function doDeletePicUrl(i) {
  form.value.lifePhoto.splice(i, 1)
}
function doDeleteUrl() {
  form.value.modelPicTemp = []
  form.value.modelPic = ''
}

function showPic(list, index) {
  const listArr = list.map(item => item.picUrl)
  showViewer(listArr, { index })
}

function doUploadImg(type) {
  if (!isEditable.value.modelPicTemp && type == 'avatar') return ElMessage.warning('暂无权限')
  if (!isEditable.value.lifePhoto && type == 'life') return ElMessage.warning('暂无权限')
  DragUploadDialogRef.value.open()
  uploadListTypes.value = type
  if (type == 'video') {
    uploadTitle.value = '上传视频封面图'
    uploadLimit.value = 1
    uploadListTypes.value = type
  } else if (type == 'avatar') {
    uploadTitle.value = '上传头像'
    uploadLimit.value = 1
    uploadListTypes.value = type
  } else if (type == 'life') {
    uploadTitle.value = '上传生活照'
    uploadLimit.value = 30 - form.value.lifePhoto.length
    uploadListTypes.value = type
  }
}
function pasteUploadChange(data) {
  if (data && data.length > 0) {
    dialogVideoForm.value.picUrl = data[0].data?.picUrl || ''
  }
}

//上传图片成功回调
function uploadInvoice(data, close) {
  if (uploadListTypes.value == 'video') {
    dialogVideoForm.value.picUrl = data[0].picUrl
    dialogVideoForm.value.id = data[0].id
  } else if (uploadListTypes.value == 'avatar') {
    form.value.modelPicTemp = data
  } else if (uploadListTypes.value == 'life') {
    form.value.lifePhoto.push(...data)
  }
  close()

  // financeUploadInvoice(upInvoiceId.value, data).then(res => {
  //   handleQuery()
  // }).finally(() => close())
}
function deleteImg() {
  dialogVideoForm.value.picUrl = ''
  dialogVideoForm.value.id = ''
}
function closeVideoDialog() {
  dialogVideoForm.value = {
    name: '',
    videoUrl: '',
    picUrl: '',
    id: '',
  }
  dialogVideoFormRef.value.resetFields()
  dialogFormVisible.value = false
}
function confirmAddVedio() {
  dialogVideoFormRef.value.validate(valid => {
    const data = { ...dialogVideoForm.value }
    if (valid) {
      if (videoType.value == 'add') {
        // dialogType.value == 'amazon' ? form.value.amazonVideo.push(data) : form.value.tiktokVideo.push(data)
        dialogType.value == 'amazon'
          ? form.value.amazonVideo.unshift(data)
          : form.value.tiktokVideo.unshift(data)
        // form.value.amazonVideo.forEach((item, index) => {
        //   item.id = index + Math.random().toString(36).substring(2)
        // })
        // form.value.tiktokVideo.forEach((item, index) => {
        //   item.id = index + Math.random().toString(36).substring(2)
        // })
      } else {
        dialogType.value == 'amazon'
          ? form.value.amazonVideo.splice(editVideoIndex.value, 1, data)
          : form.value.tiktokVideo.splice(editVideoIndex.value, 1, data)
      }
      closeVideoDialog()
    }
  })
}
const editVideoIndex = ref()
function editSelectImgInfo(data, index, type) {
  dialogVideoForm.value.name = data.name || ''
  dialogVideoForm.value.videoUrl = data.videoUrl || ''
  dialogVideoForm.value.picUrl = data.picUrl || ''
  dialogVideoForm.value.id = data.id || ''
  editVideoIndex.value = index
  doShowVideoDialog(type, 'edit')
}

const dialogFormVisible = ref(false)
const dialogForm = ref({
  tags: [],
})
const dialogTitle = ref('添加模特标签')
const dialogPlacehoder = ref('请选择模特标签')
function addTags(arr, t) {
  dialogForm.value.tags = arr.map(item => item.id || item.dictId)
  dialogForm.value.type = t
  dialogFormVisible.value = true
  if (t === 1) {
    dialogTitle.value = '添加擅长品类'
    dialogPlacehoder.value = '请选择擅长品类'
  }
  if (t === 2) {
    dialogTitle.value = '添加模特标签'
    dialogPlacehoder.value = '请选择模特标签'
  }
}
function confirmAddTag() {
  if (dialogForm.value.type === 1) {
    form.value.specialtyCategory = filterTagsList(modelCategoryList.value)
  }
  if (dialogForm.value.type === 2) {
    form.value.tags = filterTagsList(modelTagsList.value)
  }
  dialogFormVisible.value = false
}
function filterTagsList(arr) {
  if (!arr.length) return []
  let list = []
  arr.forEach(item => {
    if (dialogForm.value.tags.includes(item.id)) {
      list.push(item)
    }
    if (item.children && item.children.length) {
      list.push(...filterTagsList(item.children))
    }
  })
  return list
}
function filterTreeNode(value, data) {
  if (!value) return true
  return data.name.includes(value)
}
function closeTags(item, i) {
  item.splice(i, 1)
}
// 添加/删除 行程
function addTravels() {
  form.value.travels.push({
    date: [],
    remark: '',
  })
}
function delTravels(i) {
  form.value.travels.splice(i, 1)
}
// 添加/删除 案例视频
function addVedeoList(type) {
  if (form.value[type].length <= 10) {
    form.value[type].push(initVideoListItem())
  }
}
function delVedeoList(type, i) {
  form.value[type].splice(i, 1)
}

// 保存提交
function onSubmit() {
  formRef.value.validate((valid, fields) => {
    if (valid) {
      let {
        travels,
        specialtyCategory,
        tags,
        lifePhoto,
        modelPicTemp,
        modelPic,
        platform,
        persons,
        amazonVideo,
        tiktokVideo,
        tempTags,

        ...params
      } = form.value
      if (!travels?.length) {
        travels = []
      }
      let data = {
        ...params,
        // 行程时间
        travels:
          travels.length == 1 && !travels[0].date.length
            ? []
            : travels.map(item => {
                return {
                  startTime: item.date[0],
                  endTime: item.date[1],
                  remark: item.remark,
                }
              }),
        platform: platform.length ? platform.join(',') : '',
        persons: [persons],
        lifePhoto: lifePhoto.map(item => item.picUrl),
        modelPic: modelPicTemp[0].picUrl,
        tags:
          tempTags && tempTags.length > 0
            ? tempTags.map(item => {
                if (item.id) return { id: item.id }
                else return { name: item.name }
              })
            : [],
        specialtyCategory,
        // 亚马逊平台案例视频
        amazonVideo: amazonVideo
          .filter(item => item.videoUrl)
          .map((item, index) => ({
            name: item.name,
            picUri: item.picUrl,
            videoUrl: item.videoUrl,
            sort: amazonVideo.length - index,
          })),
        // tiktok平台案例视频
        tiktokVideo: tiktokVideo
          .filter(item => item.videoUrl)
          .map((item, index) => ({
            name: item.name,
            picUri: item.picUrl,
            videoUrl: item.videoUrl,
            sort: tiktokVideo.length - index,
          })),
      }
      // if (data.nation != '7' || data.nation != '1') {
      //   data.state = ''
      // }
      // if (data.nation != '2' || data.nation != '4') {
      //   data.province = ''
      // }
      if (data.cooperation) {
        delete data.cooperation
      }
      proxy.$confirm('确认提交保存！', '提示', {}).then(() => {
        loading.value = true
        disabled.value = true
        if (route.params.modelId && route.name != 'HandleAddModel') {
          editModel(data)
            .then(res => {
              if (res.code == 200) {
                ElMessage({
                  message: '保存成功！',
                  type: 'success',
                })
                init()
                // if (route.name == 'HandleEditModel') {
                //   form.value.oldAmazonVideo = data.amazonVideo || []
                //   form.value.oldTiktokVideo = data.tiktokVideo || []
                //   form.value.oldSpecialtyCategory = data.specialtyCategory || []
                //   form.value.oldTags = data.tags || []
                  
                // }
                // proxy.$tab.closeOpenPage({ path: '/model/modelManage' })
              }
            })
            .finally(() => {
              loading.value = false
              disabled.value = false
            })
        } else {
          addModel(data)
            .then(res => {
              if (res.code == 200) {
                ElMessage({
                  message: '新增成功！',
                  type: 'success',
                })
                proxy.$tab.closeOpenPage({ path: '/model/model/modelManage' })
              }
            })
            .finally(() => {
              loading.value = false
              disabled.value = false
            })
        }
      })
    } else {
      ElMessage({
        message: '请完成必填项！',
        type: 'error',
      })
      // console.log('error submit!', fields)
    }
  })
}

function goBackPage() {
  proxy.$tab.closeOpenPage({ path: '/model/modelManage' })
}
init()
</script>

<style lang="scss" scoped>
@import '@/assets/styles/form-disabled.scss';
// @import '@/assets/styles/customForm.scss';
.model-edit-page {
  padding: 20px;
  position: relative;

  .detail-tag {
    margin: 0 0 15px 10px;
    color: #999;
    .left-btn {
      margin-right: 20px;
      cursor: pointer;
    }
    .right-btn {
      cursor: pointer;
    }
    .active {
      color: #409eff;
      font-weight: 800;
    }
  }

  .card-head {
    font-size: 16px;
    font-weight: 800;
    margin-bottom: 5px;
  }

  .info-one {
    display: flex;
    .one-left {
      width: 380px;
      &-top {
        :deep(.el-form-item__error) {
          margin-left: 90px;
        }
      }
    }
    .one-center {
      min-width: 600px;
    }
    .one-right {
      // min-width: 600px;
    }
  }
  .info-center {
    display: flex;
    justify-content: flex-start;
    .label-text {
      .label-tips {
        margin-top: -5px;
        font-size: 12px;
        font-weight: 400;
        line-height: 11px;
        word-break: break-all;
        max-width: 110px;
      }
    }
    :deep(.el-form-item__label) {
      justify-content: flex-start;
    }
    .center-left {
      flex: 1;
    }
    .center-right {
      flex: 1;
    }
  }
  .info-footer {
    display: flex;
    justify-content: flex-start;
    .footer-left {
      flex: 1;
    }
    .footer-right {
      flex: 1;
    }
    .footer-text {
      margin-bottom: 10px;
      font-size: 12px;
      color: #999;
    }
    .image-list {
      // height: 70px;
      position: relative;
      margin-right: 8px;
      .icon {
        height: 17px;
        width: 17px;
        cursor: pointer;
      }
      .image-modal {
        position: absolute;
        bottom: 14px;
        right: 2px;

        z-index: 9;
      }
    }
    .image-item {
      border-radius: 6px;
      box-sizing: border-box;
      width: 100px;
      height: 100px;
      cursor: pointer;
      // margin-right: 10px;
    }
  }

  .title2 {
    margin: 4px 0 12px;
    font-size: 15px;
    padding-left: 50px;
    font-family: var(--el-font-family);
  }

  .tips {
    width: 100%;
    font-size: 12px;
    color: #9a9a9a;
    line-height: 20px;
    margin-top: 8px;
  }

  .videoUpItem {
    margin-bottom: 8px;
    gap: 10px;
    width: 100%;

    .el-input {
      flex-grow: 1;
    }
    .el-icon {
      cursor: pointer;
    }
  }

  :deep(.el-upload-list__item) {
    width: 100px;
    height: 100px;

    img {
      object-fit: fill;
    }
  }

  :deep(.el-upload--picture-card) {
    width: 100px;
    height: 100px;
  }
  .modelPicTempPic {
    margin-left: 90px;
    :deep(.el-upload-list__item) {
      width: 75px;
      height: 100px;

      img {
        object-fit: fill;
      }
    }

    :deep(.el-upload--picture-card) {
      width: 75px;
      height: 100px;
    }
    .image-list {
      // height: 70px;
      position: relative;
      margin-right: 8px;
      .icon {
        height: 17px;
        width: 17px;
        cursor: pointer;
      }
      .image-modal {
        position: absolute;
        top: 4px;
        right: 4px;

        z-index: 9;
      }
    }
    .image-item {
      border-radius: 6px;
      box-sizing: border-box;
      width: 100px;
      height: 100px;
      cursor: pointer;
      // margin-right: 10px;
    }
  }
}
.dialog-content {
  margin: 0 0 20px;
  font-weight: 800;
  .img {
    width: 80px;
    height: 80px;
  }
}
.upload-img {
  position: relative;
  .img-icon {
    cursor: pointer;
    position: absolute;
    bottom: 15px;
    right: 5px;
    z-index: 9;
  }
  .img-error {
    width: 80px;
    height: 80px;
    background: var(--el-fill-color-light);
    text-align: center;
    line-height: 80px;
    color: #ababb2;
  }
}
//上传样式
.image-upload {
  box-sizing: border-box;
  display: flex;
  justify-content: center;
  align-items: center;
  border: 1px dashed var(--el-border-color-darker);
  width: 100px;
  height: 100px;
  border-radius: 6px;
  background-color: var(--el-fill-color-lighter);
  cursor: pointer;
  margin-bottom: 10px;
  &:hover {
    border-color: #409eff;
  }
}
</style>
