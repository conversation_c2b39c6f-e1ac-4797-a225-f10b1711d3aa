<template>
  <el-dialog
    v-model="dialogVisible"
    title="模特状态"
    width="500"
    append-to-body
    align-center
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div style="height: 430px">
      <div
        v-if="msgData"
        :style="{
          marginLeft: curStatus == 2 ? '75px' : curStatus == 1 ? '155px' : curStatus == 3 ? '190px' : '0px',
        }"
        class="msg-box"
      >
        {{ msgData }}
        <div class="msg-box-c" :style="{ left: curStatus == 3 ? '85%' : '150px' }"></div>
      </div>
      <el-form ref="formRef" :model="form" label-width="100px" :rules="rules">
        <el-form-item label="选择状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio-button :value="0">正常合作</el-radio-button>
            <el-radio-button :value="2">行程中</el-radio-button>
            <el-radio-button :value="1">暂停合作</el-radio-button>
            <el-radio-button :value="3">取消合作</el-radio-button>
          </el-radio-group>
        </el-form-item>
        <div
          style="text-align: center; margin-top: 150px"
          v-if="form.status == '0' && modelData.isInitiator == 1"
        >
          <div style="margin-bottom: 30px">修改模特为正常可接单状态</div>
          <div>
            <el-radio @click.prevent="form.isSync = !form.isSync" v-model="form.isSync" :value="true">
              <!-- 同步开启所有家庭成员为正常状态 -->
              同步开启正常和行程中的家庭成员为正常状态
            </el-radio>
            <!-- </el-radio-group> -->
          </div>
        </div>
        <div v-if="form.status != '0'" class="status-tips">
          <span v-if="form.status == '2'">修改模特状态为"行程中"，修改后，该时间范围内模特无法接单。</span>
          <span v-if="form.status == '1'">
            修改模特状态为"暂停合作"
            <br />
            修改后，意向、预选、拍摄模特将无法选择该模特
          </span>
          <span v-if="form.status == '3'">
            修改模特状态为"取消合作"，修改后，意向、预选、拍摄模特
            <br />
            将无法选择该模特，同时模特将无法登录模特端。
          </span>
        </div>

        <el-form-item label="开始时间" prop="startTime" v-if="form.status == '2'">
          <el-date-picker
            v-model="form.startTime"
            style="width: 335px"
            type="date"
            placeholder="选择日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          ></el-date-picker>
        </el-form-item>
        <el-form-item label="结束时间" prop="endTime" v-if="form.status == '2'">
          <el-date-picker
            v-model="form.endTime"
            style="width: 335px"
            type="date"
            placeholder="选择日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          ></el-date-picker>
        </el-form-item>
        <div class="reasons-box">
          <el-form-item label="取消人" prop="cancelCooperationType" v-if="form.status == '3'">
            <el-radio-group v-model="form.cancelCooperationType" @click="handleChangeType">
              <el-radio-button :value="0">平台</el-radio-button>
              <el-radio-button :value="1">模特</el-radio-button>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="取消原因" prop="reasons" v-if="form.status == '3'">
            <!-- <el-radio-group v-model="form.cancelCooperationType" @click="handleChangeType">
              <el-radio-button :value="0">我们取消合作</el-radio-button>
              <el-radio-button :value="1">模特取消合作</el-radio-button>
            </el-radio-group> -->
            <!-- <div style="width: 100%">
            <el-button type="text" @click="handleChangeType('0')">我们取消合作</el-button>
          </div> -->
            <template #label>
              <div>
                <div>取消原因</div>
                <div style="color: #7f7f7f; font-size: 12px; text-align: center; margin-top: -15px">
                  (可多选)
                </div>
              </div>
            </template>
            <div v-if="form.cancelCooperationType == '0'">
              <el-checkbox-group v-model="form.reasons">
                <el-checkbox-button
                  v-for="item in biz_we_cancel_type"
                  :key="item.value"
                  :label="item.value"
                  :value="item.value"
                >
                  {{ item.label }}
                </el-checkbox-button>
              </el-checkbox-group>
            </div>
            <!-- <el-button type="text" @click="handleChangeType('1')">模特取消合作</el-button> -->
            <div v-if="form.cancelCooperationType == '1'">
              <el-checkbox-group v-model="form.reasons">
                <el-checkbox-button
                  v-for="item in biz_model_cancel_type"
                  :key="item.value"
                  :label="item.value"
                  :value="item.value"
                >
                  {{ item.label }}
                </el-checkbox-button>
              </el-checkbox-group>
            </div>
          </el-form-item>
        </div>
        <el-form-item label="说明" prop="statusExplain" v-if="form.status != '0'">
          <el-input
            v-model="form.statusExplain"
            style="width: 335px"
            placeholder="请填写说明"
            type="textarea"
            :rows="4"
            maxlength="1000"
            show-word-limit
            resize="none"
          />
        </el-form-item>
        <el-form-item label="行程同步" v-if="form.status == '2' && modelData.isInitiator == 1">
          <el-radio @click.prevent="form.isSync = !form.isSync" v-model="form.isSync" :value="true">
            <span v-if="form.status == 2">同步开启正常和行程中的家庭成员为行程中状态</span>
            <span v-if="form.status == 1">同步开启所有家庭成员为暂停合作</span>
            <span v-if="form.status == 3">同步开启所有家庭成员为取消合作</span>
          </el-radio>
        </el-form-item>
      </el-form>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button v-btn @click="dialogVisible = false">取消</el-button>
        <el-button v-btn type="primary" @click="save">保存</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { checkPermi } from '@/utils/permission'
import { updateModelStatus, getModelMsg } from '@/api/model/model'

const { proxy } = getCurrentInstance()

const { biz_we_cancel_type, biz_model_cancel_type } = proxy.useDict(
  'biz_we_cancel_type',
  'biz_model_cancel_type'
)

const emits = defineEmits(['success'])

const dialogVisible = ref(false)
const formRef = ref(null)
const modelId = ref('')
const form = ref({
  isSync: false,
  cancelCooperationType: 0,
  cancelCooperationSubType: '',
  status: '',
  startTime: '',
  endTime: '',
  statusExplain: '',
  reasons: [],
})

const rules = {
  status: [{ required: true, message: '请选择状态', trigger: 'blur' }],
  startTime: [{ required: true, message: '请选择开始时间', trigger: 'change' }],
  endTime: [{ required: true, message: '请选择结束时间', trigger: 'change' }],
  statusExplain: [{ required: true, message: '请填写说明', trigger: 'blur' }],
  cancelCooperationType: [{ required: true, message: '请选择取消人', trigger: 'change' }],
  reasons: [{ required: true, message: '请选择原因', trigger: 'change' }],
}

const modelData = ref({})
const curModelIsFamily = ref(false)
const msgData = ref('')
const curStatus = ref('')
function open(row, status, travels, statusExplain, cancelTypeList, cancelType) {
  modelId.value = row.id
  modelData.value = row
  curStatus.value = status
  form.value.status = status
  if (status != 0) {
    form.value.statusExplain = statusExplain
  }
  if (status == 2) {
    form.value.startTime = travels?.startTime
    form.value.endTime = travels?.endTime
  }
  if (status == 3) {
    form.value.cancelCooperationType = cancelType || 0
    form.value.cancelCooperationSubType = cancelTypeList || []
    form.value.reasons = cancelTypeList?.split(',') || []
  }
  if (row.isFamilyModel == 1 && row.isInitiator == 0) {
    curModelIsFamily.value = true
    getModelMsg(row.id).then(res => {
      msgData.value = res.data || ''
    })
  }
  dialogVisible.value = true
}

function close() {
  dialogVisible.value = false
}

function handleClose() {
  modelId.value = ''
  form.value.status = ''
  form.value.startTime = ''
  form.value.endTime = ''
  form.value.statusExplain = ''
  form.value.reasons = []
  form.value.cancelCooperationType = 0
  form.value.cancelCooperationSubType = ''
  form.value.isSync = ''
  modelData.value = {}
  msgData.value = ''
  curModelIsFamily.value = false
  curStatus.value = ''
}

function handleChangeType(val) {
  // form.value.cancelCooperationType = val
  form.value.reasons = []
}

function handleChangeSyncStatus(val) {
  console.log(val, form.value.isSync)
}

function save() {
  switch (form.value.status) {
    case 0:
      if (!checkPermi(['model:manage:status-normal'])) return proxy.$modal.msgWarning('暂无操作权限')
      break
    case 2:
      if (!checkPermi(['model:manage:status-journey'])) return proxy.$modal.msgWarning('暂无操作权限')
      break
    case 1:
      if (!checkPermi(['model:manage:status-pause'])) return proxy.$modal.msgWarning('暂无操作权限')
      break
    case 3:
      if (!checkPermi(['model:manage:status-cancel'])) return proxy.$modal.msgWarning('暂无操作权限')
      break
  }

  formRef.value.validate(valid => {
    if (valid) {
      proxy.$modal.confirm('确认保存修改！', '提示', {}).then(() => {
        proxy.$modal.loading('保存中')

        let params = {
          id: modelId.value,
          status: form.value.status,
          isSync: form.value.isSync,
        }
        if (form.value.status == 2) {
          params.startTime = form.value.startTime
          params.endTime = form.value.endTime
        }
        if (form.value.status != 0) {
          params.statusExplain = form.value.statusExplain
        }
        if (form.value.status == '3') {
          params.cancelCooperationSubType = form.value.reasons.join(',')
          params.cancelCooperationType = form.value.cancelCooperationType
        }
        if (form.value.status == 1 || form.value.status == 3) delete params.isSync
        updateModelStatus(params)
          .then(res => {
            proxy.$modal.msgSuccess('保存成功')
            emits('success')
            close()
          })
          .finally(() => proxy.$modal.closeLoading())
      })
    }
  })
}

defineExpose({
  open,
  close,
})
</script>

<style scoped lang="scss">
.msg-box {
  margin-bottom: 10px;
  color: #fff;
  font-size: 12px;
  background: #a8a8a8;
  padding: 5px 10px;
  // width: 300px;
  // padding-left: 16px;
  border-radius: 20px;
  position: relative;
  display: inline-block;
  z-index: 3;
  .msg-box-c {
    position: absolute;
    left: 150px;
    bottom: -5px;
    width: 0;
    height: 0;
    border-left: 10px solid transparent; /* 左边透明 */
    border-right: 10px solid transparent; /* 右边透明 */
    border-top: 10px solid #a8a8a8; /* 上边为颜色 */
    transform: translateX(-50%); /* 让三角形居中 */
    z-index: 1;
  }
}
.status-tips {
  width: 350px;
  font-size: 12px;
  color: #d9001b;
  margin: -10px 0 8px 100px;
}
.reasons-box {
  :deep(.el-radio-button) {
    margin: 0 10px 10px 0;

    &.is-active {
      box-shadow: none;
    }

    .el-radio-button__inner {
      border-left: var(--el-border);
      border-radius: var(--el-border-radius-base);
      box-shadow: 0 0 0 0;
    }
  }
  :deep(.el-radio-group) {
    .el-radio-button.is-active {
      .el-radio-button__inner {
        box-shadow: none;
      }
    }
  }

  :deep(.el-checkbox-button) {
    margin: 0 10px 10px 0;

    &.is-checked {
      box-shadow: none;
    }

    .el-checkbox-button__inner {
      font-weight: 500;
      border-left: var(--el-border);
      border-radius: var(--el-border-radius-base);
    }
  }

  :deep(.el-checkbox-group) {
    .el-checkbox-button.is-checked {
      .el-checkbox-button__inner {
        box-shadow: none;
      }
    }
    .el-checkbox-button.is-focus {
      .el-checkbox-button__inner {
        box-shadow: none;
        border-left-color: #409eff;
      }
    }
  }
}
</style>
