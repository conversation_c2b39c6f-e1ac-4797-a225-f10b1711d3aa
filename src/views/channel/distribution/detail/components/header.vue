<template>
  <div class="header-detail">
    <div class="detail-title">渠道信息</div>
    <div class="detail-content" v-if="props.detailType === 'distribution'">
      <el-row>
        <el-col :span="8">
          <div class="label">账号ID：</div>
          <div class="content">{{ data.id }}</div>
        </el-col>
        <el-col :span="8">
          <div class="label">渠道名称：</div>
          <div class="content">{{ data.channelName }}</div>
        </el-col>
        <el-col :span="8">
          <div class="label">账号/种草码：</div>
          <div class="content">{{ data.seedCode }}</div>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <div class="label">手机号：</div>
          <div class="content">{{ data.phone }}</div>
        </el-col>
        <el-col :span="8">
          <div class="label">结算方案：</div>
          <div class="content">
            {{
              data.settleDiscountType == 1
                ? `固定金额${data.brokeRage >= 0 ? data.brokeRage : '-'}元`
                : `固定比例${data.brokeRage >= 0 ? data.brokeRage : '-'}%`
            }}
          </div>
        </el-col>
        <el-col :span="8">
          <div class="label">备注：</div>
          <div class="content">{{ data.remark }}</div>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <div class="label">创建时间：</div>
          <div class="content">{{ data.createTime }}</div>
        </el-col>
        <el-col :span="8">
          <div class="label">专属链接：</div>
          <div class="content">{{ data.dedicatedLink }}</div>
        </el-col>
        <el-col :span="8">
          <div class="label">海报：</div>
          <el-button link type="primary" v-btn @click="handleUrlView">查看</el-button>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <div class="label">创建人：</div>
          <div class="content">{{ data.createBy }}</div>
        </el-col>
      </el-row>
    </div>
    <div class="detail-content" v-else>
      <el-row>
        <el-col :span="8">
          <div class="label">渠道ID：</div>
          <div class="content">{{ data.id }}</div>
        </el-col>
        <el-col :span="8">
          <div class="label">投放平台：</div>
          <div class="content">
            {{ platformList.find(item => item.value == data.marketingPlatform)?.label || '-' }}
          </div>
        </el-col>
        <el-col :span="8">
          <div class="label">渠道名称：</div>
          <div class="content">
            {{ data.marketingChannelName }}
          </div>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <div class="label">落地形式：</div>
          <div class="content">
            {{ landingList.find(item => item.value == data.landingForm)?.label || '-' }}
          </div>
        </el-col>
        <el-col :span="16">
          <div class="label">备注：</div>
          <div class="content">{{ data.remark }}</div>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24" v-if="data.landingForm == 1">
          <div class="label">投流链接：</div>
          <div class="content">{{ data.dedicatedLinkCode }}</div>
        </el-col>
        <el-col :span="24" v-if="data.landingForm == 2" style="align-items: center">
          <div class="label">二维码：</div>
          <div class="content">
            <el-button v-btn link type="primary" @click="DownloadImg(data)">下载</el-button>
          </div>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup>
// import QRCode from 'qrcode'
// import { useViewer } from '@/hooks/useViewer'
import { platformList, landingList } from '@/views/channel/market/data'
import { downUrlFile } from '@/utils/download'
// const { showViewer } = useViewer()

const props = defineProps({
  data: {
    type: Object,
    default: () => ({}),
  },
  detailType: {
    type: String,
    default: 'distribution',
  },
})

const emits = defineEmits(['checkUrlView'])

async function DownloadImg(data) {
  // const dataUrl = await QRCode.toDataURL(url, {
  //   errorCorrectionLevel: 'H',
  //   width: '100%',
  // })
  downUrlFile(data.dedicatedLinkCode, `${data.marketingChannelName}.png`)
}

async function handleUrlView() {
  // const dataUrl = await QRCode.toDataURL(props.data.weChatUrl, {
  //   errorCorrectionLevel: 'H',
  //   width: '100%',
  // })
  emits('checkUrlView', props.data.weChatUrl)
}
</script>

<style scoped lang="scss">
.header-detail {
  background-color: #fff;
  border-radius: 4px;
  gap: 10px;
  position: relative;
  box-shadow: var(--el-box-shadow-light);
}
.detail-title {
  padding: 13px;
  border-bottom: 1px solid #e8e8e8;
}
.detail-content {
  padding: 15px 13px 13px;
  .el-col {
    align-items: baseline;
  }
  .el-col-8,
  .el-col-16,
  .el-col-24 {
    display: flex;
    font-size: 13px;
    margin-bottom: 15px;
    // align-items: center;

    .label {
      color: #999;
      flex-shrink: 0;
      width: 130px;
      text-align: right;
    }
    .content {
      flex-shrink: 0;
      width: calc(100% - 130px);
      word-break: break-all;
    }
  }
}
</style>
