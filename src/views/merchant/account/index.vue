<template>
  <div style="margin: 20px">
    <ElTablePage
      ref="tableRef"
      :columns="columns"
      :data="tableData"
      :loading="tableLoading"
      :currentPage="currentPage"
      :pageSize="pageSize"
      :total="total"
      :tableAction="{
        width: '190',
        fixed: 'right',
      }"
      @page-change="pageChange"
      row-key="id"
    >
      <template #tableHeader>
        <div>
          <el-form :inline="true" :model="queryParams" @submit.prevent>
            <el-form-item>
              <el-select v-model="queryParams.accountType" placeholder="请选择身份" clearable>
                <el-option
                  v-for="dict in identityList"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-select v-model="queryParams.channelType" placeholder="请选择来源" clearable>
                <el-option
                  v-for="dict in channelTypeList"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="" prop="status">
              <el-select
                v-model="queryParams.status"
                placeholder="请选择账号状态"
                clearable
                style="width: 200px"
              >
                <el-option
                  v-for="item in accStatusList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-input
                v-model="queryParams.searchName"
                style="min-width: 400px"
                clearable
                placeholder="搜索ID/姓名/微信名/手机号/企业名/客服名称"
              ></el-input>
            </el-form-item>
            <el-form-item>
              <el-button v-btn type="primary" icon="Search" native-type="submit" @click="onQuery">
                搜索
              </el-button>
              <el-button v-btn icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
          <div class="flex-between">
            <el-button
              style="margin-bottom: 10px"
              type="primary"
              v-btn
              icon="plus"
              v-hasPermi="['merchant:account:add']"
              @click="goAccountDialog('add', '')"
            >
              新增账号
            </el-button>
            <div>
              已有账号
              <span style="color: #70b603">{{ total }}</span>
              个
            </div>
          </div>
        </div>
      </template>
      <template #status="{ row }">
        <!-- style="--el-switch-off-color: #ff4949" -->
        <el-switch
          :model-value="row.status"
          :active-value="0"
          :inactive-value="1"
          @click="switchChange(row)"
        />
      </template>
      <template #tableAction="{ row }">
        <el-button
          v-btn
          link
          type="primary"
          @click="goAccountDetailDialog(row)"
          v-hasPermi="['merchant:account:detail']"
        >
          查看
        </el-button>
        <el-button
          v-btn
          link
          type="primary"
          @click="goAccountDialog('edit', row)"
          v-hasPermi="['merchant:account:edit']"
        >
          编辑
        </el-button>
        <el-button
          v-btn
          link
          type="primary"
          @click="handlePhoneDialog(row)"
          v-hasPermi="['merchant:account:edit:phone']"
        >
          修改手机号
        </el-button>
      </template>
    </ElTablePage>
    <AccountDialog :title="dialogTitle" ref="AccountDialogRef" @success="handleQuery" />
    <el-dialog
      title="查看账号信息"
      v-model="showDetailDialog"
      width="600px"
      align-center
      :showClose="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <Title style="margin: 0">基础信息</Title>
      <el-row>
        <el-col :span="12">姓名：{{ detailData.name || '-' }}</el-col>
        <el-col :span="12">账号：{{ detailData.id }}</el-col>
      </el-row>
      <el-row>
        <el-col :span="12">绑定微信：{{ detailData.nickName }}</el-col>
        <el-col :span="12">手机号：{{ detailData.phone }}</el-col>
      </el-row>
      <el-row>
        <el-col :span="24">公司名称：{{ detailData.businessName || '-' }}</el-col>
        <!-- <el-col :span="12">公司名称：{{ detailData.businessName }}</el-col> -->
      </el-row>
      <el-row>
        <el-col :span="12">
          账号类型：
          <template v-for="dict in identityList" :key="dict.value">
            <span v-if="dict.value == detailData.accountType">{{ dict.label }}</span>
          </template>
        </el-col>
        <el-col :span="12">
          重要程度：
          <template v-for="dict in customerTypeList" :key="dict.value">
            <span v-if="dict.value == detailData.customerType">{{ dict.label }}</span>
          </template>
        </el-col>
      </el-row>
      <el-row>
        <!-- <el-col :span="12">
          账号身份：
          <template v-for="dict in merchantTypeList" :key="dict.value">
            <span v-if="dict.value == detailData.isProxy">{{ dict.label }}</span>
          </template>
        </el-col> -->
        <el-col :span="12">对接客服：{{ detailData.waiterName || '-' }}</el-col>
        <el-col :span="12">注册时间：{{ detailData.createTime }}</el-col>
      </el-row>
      <el-row>
        <el-col :span="12">售前：{{ detailData.connectUserName || '-' }}</el-col>
      </el-row>
      <template #footer>
        <div class="flex-end btn">
          <slot name="button">
            <el-button v-btn plain @click="showDetailDialog = false" round>关闭</el-button>
          </slot>
        </div>
      </template>
    </el-dialog>
    <el-dialog
      title="修改手机号"
      v-model="showEditPhoneDialog"
      width="450px"
      align-center
      destroy-on-close
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="closePhoneDialog"
    >
      <el-form ref="phoneFormRef" :model="phoneForm" :rules="phoneRules" label-width="100px" @submit.prevent>
        <el-form-item label="手机号" prop="phone">
          <el-input
            v-model="phoneForm.phone"
            placeholder="请输入手机号"
            style="width: 280px"
            maxlength="11"
            clearable
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="flex-end btn">
          <slot name="button">
            <el-button v-btn plain @click="closePhoneDialog">取消</el-button>
            <el-button v-btn plain type="primary" @click="handleEditPhone" :loading="phoneLoading">
              确定
            </el-button>
          </slot>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import ElTablePage from '@/components/Table/ElTablePage.vue'
import AccountDialog from '@/views/merchant/account/dialog/accountDialog.vue'
import Title from '@/components/Public/title'
import { bizUserList, editBizUserPhone, editBizUserStatus } from '@/api/merchant/merchant'
import { identityList, channelTypeList, customerTypeList, accStatusList } from '../data.js'
import { ElMessage } from 'element-plus'
import auth from '@/plugins/auth'
const { proxy } = getCurrentInstance()
const tableData = ref([])
const tableLoading = ref(false)
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)
const dialogTitle = ref('')
const AccountDialogRef = ref(null)
const showEditPhoneDialog = ref(false)
const phoneLoading = ref(false)
const phoneFormRef = ref(null)
const phoneForm = ref({
  phone: '',
  id: '',
})

const queryParams = ref({
  searchName: '',
  accountType: '',
  channelType: '',
  status: '',
})

const columns = ref([
  {
    label: 'ID',
    prop: 'id',
    width: '80',
  },
  {
    label: '姓名',
    prop: 'name',
    minWidth: '150',
    ellipsis: true,
    handle: (data, row) => {
      return row.name ? row.name : '-'
    },
  },
  {
    label: '微信名',
    prop: 'nickName',
    minWidth: '150',
    handle: (data, row) => {
      return row.nickName ? row.nickName : '-'
    },
  },
  {
    label: '所属企业',
    prop: 'businessName',
    minWidth: '230',
    ellipsis: true,
    handle: (data, row) => {
      return row.businessName ? row.businessName : '-'
    },
  },
  {
    label: '手机号',
    prop: 'phone',
    width: '120',
  },
  {
    label: '售前',
    prop: 'connectUserName',
    width: '120',
    handle: (data, row) => {
      return row.connectUserName ? row.connectUserName : '-'
    },
  },
  {
    label: '类型',
    prop: 'accountType',
    width: '200',
    handle: status => {
      let s = identityList.find(item => item.value == status)
      return s ? s.label : '-'
    },
  },
  // {
  //   label: '客服',
  //   prop: 'waiterName',
  //   minWidth: '200',
  //   handle: (data, row) => {
  //     return row.waiterName ? row.waiterName : '-'
  //   },
  // },
  {
    label: '来源',
    prop: 'channelType',
    width: '200',
    handle: (data, row) => {
      if (row.channelType == 1) {
        return row.channelName ? 'SC-' + row.channelName : '-'
      }
      if (row.channelType == 2) {
        return row.channelName ? 'FX-' + row.channelName : '-'
      }
      if (row.channelType == 7) {
        return row.nickName ? 'LB-' + row.channelName : '-'
      }
      let s = channelTypeList.find(item => item.value == row.channelType)
      if (s) {
        return s.label
      }
      return '-'
    },
  },
  { slot: 'status', prop: 'status', label: '账号状态', width: '80' },
  {
    label: '注册时间',
    minWidth: '200',
    prop: 'createTime',
  },
])

const phoneRules = {
  phone: [
    { required: true, message: '请输入正确的手机号码', trigger: 'blur' },
    { pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/, message: '请输入正确的手机号码', trigger: 'change' },
  ],
}

function onQuery() {
  currentPage.value = 1
  handleQuery()
}
function handleQuery() {
  tableLoading.value = true
  queryParams.value.pageNum = currentPage.value
  queryParams.value.pageSize = pageSize.value
  queryParams.value.auditStatus = ''
  bizUserList(queryParams.value)
    .then(res => {
      if (res.data.rows && res.data.rows.length > 0) {
        tableData.value = res.data.rows
        total.value = res.data.total
        // if(accountId.value) {
        //   goAccountDetailDialog(tableData.value[0] || {})
        // }
      } else {
        tableData.value = []
        total.value = 0
      }
    })
    .catch(err => {})
    .finally(() => (tableLoading.value = false))
}

const pageChange = page => {
  currentPage.value = page.currentPage
  pageSize.value = page.pageSize
  handleQuery()
}

//重置
function resetQuery() {
  queryParams.value.searchName = ''
  queryParams.value.accountType = ''
  queryParams.value.channelType = ''
  queryParams.value.status = ''
  currentPage.value = 1
  pageSize.value = 10
  handleQuery()
}
//详情
const showDetailDialog = ref(false)
const detailData = ref({})
function goAccountDetailDialog(data) {
  detailData.value = data
  showDetailDialog.value = true
  // accountId.value = ''
}
//弹窗
function goAccountDialog(type, data) {
  dialogTitle.value = type === 'add' ? '新增账号' : '编辑账号'
  AccountDialogRef.value.open(type, data)
}

//修改手机号
function handlePhoneDialog(data) {
  phoneForm.value.phone = data.phone
  phoneForm.value.id = data.id
  showEditPhoneDialog.value = true
}

function closePhoneDialog() {
  phoneFormRef.value.clearValidate()
  phoneForm.value.phone = ''
  showEditPhoneDialog.value = false
}

function handleEditPhone() {
  phoneFormRef.value.validate(valid => {
    if (valid) {
      phoneLoading.value = true
      editBizUserPhone(phoneForm.value)
        .then(res => {
          ElMessage.success('修改成功')
          closePhoneDialog()
          handleQuery()
        })
        .finally(() => {
          phoneLoading.value = false
        })
    }
  })
}

// 更新账号状态
function switchChange(row) {
  if (!auth.hasPermi('merchant:account:status')) {
    proxy.$modal.msgWarning('无权限操作！')
    return
  }
  let val = row.status
  let text = val == 0 ? '禁用' : '启用'
  proxy.$modal
    .confirm('确认要' + text + '' + row.nickName + '的账号吗?')
    .then(() => {
      return editBizUserStatus({ status: val ? 0 : 1, id: row.id })
    })
    .then(() => {
      proxy.$modal.msgSuccess(text + '成功')
      row.status = val
      handleQuery()
    })
    .catch(() => {
      row.status = val ? 1 : 0
    })
}
// const accountId = ref('')
function init() {
  // accountId.value = sessionStorage.getItem('accountId') || ''
  // if (accountId.value) {
  //   queryParams.value.searchName = accountId.value
  //   sessionStorage.removeItem('accountId');
  // }
  handleQuery()
}
init()
</script>

<style lang="scss" scoped>
:deep(.el-row) {
  margin: 10px 0 10px 12px;
}
</style>
