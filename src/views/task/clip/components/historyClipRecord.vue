<template>
  <div>
    <el-dialog
      align-center
      append-to-body
      title="历史剪辑记录"
      v-model="isShow"
      width="1100px"
      :close-on-click-modal="true"
      :close-on-press-escape="false"
      @close="close"
    >
      <!-- <div class="empty-box flex-center" v-if="recordList.length == 0">暂无数据</div> -->
      <div class="table-box">
        <el-table :data="recordList" ref="tableRef" border :span-method="arraySpanMethod" height="700px">
          <el-table-column label="素材提交人" align="center" prop="uploadBy">
            <template v-slot="{ row }">
              <div>
                <div>{{ row.uploadBy }}</div>
                <div>{{ row.uploadTime }}</div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="领取编码" align="center" width="80" prop="getCode">
            <template v-slot="{ row }">
              <div>{{ row.getCode || '-' }}</div>
            </template>
          </el-table-column>
          <el-table-column label="素材链接" align="center" class-name="link-box" width="200" prop="link">
            <template v-slot="{ row }">
              <div class="tag" v-if="row.rollbackId">
                <el-tag type="danger" effect="plain" size="small">回退订单</el-tag>
              </div>
              <div v-if="row.link">
                <el-link v-btn :underline="false" target="_blank" type="primary" :href="row.link">
                  {{ row.link || '-' }}
                </el-link>
              </div>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column label="领取人" align="center" prop="getBy">
            <template v-slot="{ row }">
              <div v-if="row.getBy">
                <div>{{ row.getBy }}</div>
                <div>{{ row.getTime }}</div>
              </div>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column label="剪辑人" align="center" prop="editBy">
            <template v-slot="{ row }">
              <div v-if="row.editBy">
                <div>{{ row.editBy }}</div>
                <div>{{ row.editTime }}</div>
              </div>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column label="反馈人" align="center" prop="feedbackBy" class-name="link-box">
            <template v-slot="{ row }">
              <div
                v-if="row.feedBackVOS && row.feedBackVOS.length > 0"
                style="display: flex; flex-direction: column; gap: 10px 0"
              >
                <div
                  v-for="(item, index) in row.feedBackVOS"
                  :key="item.id"
                  style="height: 90px; display: flex; flex-direction: column; justify-content: center"
                >
                  <div>{{ item.feedbackBy || '-' }}</div>
                  <div>{{ item.feedbackTime }}</div>
                  <div
                    style="width: 100%; border-bottom: 1px solid #ebeef5; position: absolute; left: 0"
                    :style="{ top: (100 / row.feedBackVOS.length) * (index + 1) + '%' }"
                    v-if="row.feedBackVOS.length != 0 && index != row.feedBackVOS.length - 1"
                  ></div>
                </div>
              </div>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column label="反馈链接" align="center" class-name="link-box">
            <template v-slot="{ row }">
              <div
                v-if="row.feedBackVOS && row.feedBackVOS.length > 0"
                style="display: flex; flex-direction: column; gap: 10px 0"
              >
                <div
                  v-for="(item, index) in row.feedBackVOS"
                  :key="item.id"
                  style="height: 90px; display: flex; flex-direction: column; justify-content: center"
                >
                  <template v-if="item.videoUrl || item.picUrl">
                    <div class="one-ell productLink" v-if="item.videoUrl">
                      视频:
                      <el-link v-btn :underline="false" target="_blank" type="primary" :href="item.videoUrl">
                        {{ item.videoUrl }}
                      </el-link>
                    </div>
                    <div class="one-ell productLink" v-if="item.picUrl">
                      照片:
                      <el-link v-btn :underline="false" target="_blank" type="primary" :href="item.picUrl">
                        {{ item.picUrl }}
                      </el-link>
                    </div>
                    <div
                      style="
                        width: 100%;
                        border-bottom: 1px solid #ebeef5;
                        position: absolute;
                        left: 0;
                      "
                      :style="{ top: (100 / row.feedBackVOS.length) * (index + 1) + '%' }"
                      v-if="row.feedBackVOS.length != 0 && index != row.feedBackVOS.length - 1"
                    ></div>
                  </template>
                  <div v-else>
                    -
                    <div
                      style="
                        width: 100%;
                        border-bottom: 1px solid #ebeef5;
                        position: absolute;
                        left: 0;
                      "
                      :style="{ top: (100 / row.feedBackVOS.length) * (index + 1) + '%' }"
                      v-if="row.feedBackVOS.length != 0 && index != row.feedBackVOS.length - 1"
                    ></div>
                  </div>
                </div>
              </div>
              <div v-else>-</div>
            </template>
          </el-table-column>
          <el-table-column label="反馈情况" align="center" prop="feedbackStatus">
            <template v-slot="{ row }">
              <div v-if="row.feedbackStatus || row.feedbackStatus == 0 || row.feedbackRemark">
                {{ feedbackStatusList.find(item => item.value == row.feedbackStatus)?.label
                }}{{ row.feedbackRemark ? '-' + row.feedbackRemark : '' }}
              </div>
              <div v-else>-</div>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { historyEditList } from '@/api/clip'

const isShow = ref(false)

const emits = defineEmits(['success'])

const tableRef = ref()
const recordList = ref([])
const feedbackStatusList = [
  {
    label: '已反馈',
    value: 1,
  },
  {
    label: '不反馈给商家',
    value: 2,
  },
  {
    label: '订单回退',
    value: 3,
  },
  {
    label: '联动关闭',
    value: 4,
  },
]
const open = videoId => {
  isShow.value = true
  getHistoryEditList(videoId)
}

const close = () => {
  recordList.value = []
  isShow.value = false
}

const confirm = () => {
  close()
}

const arraySpanMethod = ({ row, column, rowIndex, columnIndex }) => {
  // if (columnIndex == 6) {
  //   if (rowIndex === 0) {
  //     return {
  //       rowspan: 2,
  //       colspan: 1,
  //     }
  //   } else {
  //     return {
  //       rowspan: 0,
  //       colspan: 0,
  //     }
  //   }
  // }
}

function getHistoryEditList(videoId) {
  historyEditList({ videoId }).then(res => {
    recordList.value = res.data
  })
}

defineExpose({ open, close })
</script>

<style lang="scss" scoped>
.empty-box {
  font-size: 24px;
  color: #aaa;
  padding: 60px 0;
}
.table-box {
  .productLink {
    position: relative;
    padding-right: 5px;

    :deep(.el-link) {
      display: contents;

      .el-link__inner {
        display: inline;
      }
    }
  }

  :deep(.el-table) {
    .link-box {
      position: relative;
      .tag {
        position: absolute;
        top: 0;
        right: 0;
      }
      .el-link {
        display: block;
        .el-link__inner {
          display: block;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
    }
  }
}
</style>
