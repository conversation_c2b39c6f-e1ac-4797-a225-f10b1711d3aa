<template>
  <el-dialog
    v-model="dialogVisible"
    title="历史预选模特"
    width="880"
    align-center
    append-to-body
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="handleClose"
    style="font-weight: 600"
  >
    <div class="list-box" v-loading="loading">
      <el-empty
        v-if="!historyModels.length"
        description="暂无数据"
        :image-size="80"
        style="margin-top: 100px"
      ></el-empty>
      <div class="flex-column gap-10 model-box" v-for="(row, index) in historyModels" :key="row.id">
        <div class="tit">
          第{{ row.count }}次匹配 {{ !index ? '（本次）' : '' }}
          <el-tag type="danger" v-if="row.rollbackId" size="small" round>回退订单</el-tag>
        </div>
        <div
          class="model-list-item"
          v-for="m in [
            ...row.normalOrderVideoMatchPreselectModelVOS,
            ...row.outOrderVideoMatchPreselectModelVOS,
          ]"
          :key="m.id"
        >
          <div class="flex-between divider" style="padding: 10px">
            <div class="flex-start gap-10">
              <el-avatar
                class="model-avatar"
                icon="UserFilled"
                :size="40"
                :src="$picUrl + m.model?.modelPic + '!1x1compress'"
              />
              <div>
                <div class="flex-start gap-5">
                  <div>{{ m.model?.name }}</div>
                  <div style="color: var(--text-gray-color)">({{ m.model?.account }})</div>
                </div>
                <div class="flex-start gap-5" style="margin-top: 5px">
                  <model-score
                    v-if="m.modelCooperationScore || m.modelCooperationScore === 0"
                    :score="m.modelCooperationScore"
                  />
                  <!-- <biz-model-cooperation :value="m.modelCooperation" /> -->
                  <biz-model-type-new :value="m.modelType" />
                </div>
              </div>
            </div>
            <div v-if="!disabled">
              <el-button v-if="m.status === 3 && !m.active" v-btn round plain @click="handlePreselection(m)">
                恢复预选
              </el-button>
            </div>
          </div>
          <div class="flex-start gap-10">
            <div class="label">英文部客服</div>
            <div class="content">{{ m.modelPersonName }}</div>
            <div class="label">模特来源</div>
            <div class="content">{{ sourceOptions.find(item => item.value === m.addType)?.label }}</div>
            <div class="label">模特意向</div>
            <div class="content">{{ handleModelIntention(m.modelIntention) }}</div>
          </div>
          <div class="flex-start gap-10" style="margin-top: 10px" v-if="m.oustTime">
            <div class="label">淘汰时间</div>
            <div class="content">{{ m.oustTime }}</div>
            <div class="label">确认淘汰时间</div>
            <div class="content">{{ m.confirmOustTime }}</div>
          </div>
          <div class="flex-start gap-10" style="margin: 10px 0; align-items: baseline">
            <div class="label" v-if="m.status === 3">淘汰原因</div>
            <div class="remark-box" v-if="m.status === 3">
              <div>{{ handleOustRemark(m.remark, m.oustType, false) }}</div>
              <div class="text-n-all remark-content" :class="{ 'max-h': m.showRemark }">
                {{ m.remark ? handleOustRemark(m.remark, m.oustType, true) : '' }}
              </div>
            </div>
            <el-button
              v-if="m.remark && stringLength(handleOustRemark(m.remark, m.oustType, true)) > 102"
              v-btn
              link
              type="primary"
              style="transform: translateY(17px)"
              @click="() => (m.showRemark = !m.showRemark)"
            >
              {{ m.showRemark ? '收起' : '展开' }}
              <el-icon
                :style="{
                  transform: m.showRemark ? 'rotate(270deg)' : 'rotate(90deg)',
                }"
              >
                <DArrowRight />
              </el-icon>
            </el-button>
          </div>
          <div class="flex-start gap-10" style="align-items: flex-start" v-if="m.objectKey">
            <div class="label">附件图片</div>
            <ViewerImageList
              :data="m.objectKey.split(',')"
              :show-delete-btn="false"
              is-preview-all
              style="--image-width: 60px; --image-height: 60px"
            />
          </div>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script setup>
import ViewerImageList from '@/components/ImagePreview/viewerImageList.vue'
import { getHistoryMatchList, addPreselectModel } from '@/api/order/preselection'
import {
  modelIntentionOptions,
  sourceOptions,
  handleOustRemark,
  handleRestorePreselection,
} from '@/views/task/preselection/index.js'
// import { useViewer } from '@/hooks/useViewer'
import { stringLength } from '@/utils/index'

// const { showViewer } = useViewer()
const dialogVisible = ref(false)
const { proxy } = getCurrentInstance()

defineProps({
  disabled: Boolean,
})

defineExpose({
  open,
  close,
})

const emits = defineEmits(['success'])

const historyModels = ref([])
const loading = ref(false)
const videoId = ref('')

function open(id) {
  if (!id) return
  videoId.value = id
  getList()
  dialogVisible.value = true
}

function close() {
  dialogVisible.value = false
}

function handleClose() {
  historyModels.value.length = 0
  dialogVisible.value = false
}

function getList() {
  loading.value = true
  getHistoryMatchList(videoId.value)
    .then(res => {
      if (res.code === 200) {
        historyModels.value = res.data
      }
    })
    .finally(() => {
      loading.value = false
    })
}

function handleModelIntention(val) {
  let res = modelIntentionOptions.find(item => item.value == val)
  return res ? res.label : ''
}

function handlePreselection(m) {
  handleRestorePreselection(
    {
      matchId: historyModels.value[0].id,
      modelIds: [m.model.id],
    },
    () => {
      emits('success')
      close()
    }
  )
}
</script>

<style scoped lang="scss">
.list-box {
  padding-right: 10px;
  height: 550px;
  overflow-y: auto;
  font-weight: 500;

  .model-box {
    padding: 20px 0;
    align-items: flex-start;

    .divider {
      border-bottom: 1px solid #ebebeb;
      margin-bottom: 10px;
    }

    .model-list-item {
      padding: 10px;
      margin-bottom: 10px;
      width: 100%;
      box-sizing: border-box;
      border-radius: 4px;
      background-color: #f7f7f7;
    }

    .tit {
      font-size: 16px;
      color: #000;
    }
    .label {
      width: 70px;
      font-size: 13px;
      color: #848484;
    }
    .content {
      width: 178px;
      font-size: 13px;
      color: #000;
    }
    .remark-box {
      max-width: 670px;
      font-size: 13px;
      color: #000;

      .remark-content {
        max-height: 18px;
        overflow: hidden;

        &.max-h {
          max-height: 500px;
          overflow-y: auto;
        }
      }
    }
  }
}
</style>
