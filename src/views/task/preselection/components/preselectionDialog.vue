<template>
  <el-dialog
    v-model="dialogVisible"
    :title="title"
    width="1100"
    align-center
    append-to-body
    :show-close="false"
    :close-on-click-modal="true"
    :close-on-press-escape="false"
    @close="handleClose"
  >
    <template #header="{ titleId, titleClass }">
      <div class="flex-start" style="align-items: baseline" v-if="type === 'distribute'">
        <div :id="titleId" :class="titleClass" style="font-weight: 600">{{ title }}</div>
        <div style="font-size: 14px; color: #999">（选择后，订单会同步展示在模特端相关模特的推荐板块）</div>
      </div>
      <div
        class="flex-start gap-5"
        style="align-items: baseline"
        v-else-if="type === 'add' && onlyHighQuality"
      >
        <div :id="titleId" :class="titleClass" style="font-weight: 600">{{ title }}</div>
        <div style="font-size: 14px; color: var(--el-color-warning)">目前仅可选择优质模特</div>
      </div>
      <div class="flex-between" v-else>
        <div :id="titleId" :class="titleClass" style="font-weight: 600">{{ title }}</div>
        <div v-if="type === 'view'" style="font-size: 16px; color: #999; margin-right: 12px">
          已选{{ modelList.length }}名
        </div>
        <div v-if="type === 'history'" style="font-size: 16px; color: #999; margin-right: 12px">
          已选{{ modelLength }}名，已淘汰{{ outModelLength }}名
        </div>
      </div>
    </template>
    <div class="content-box">
      <div class="search-box" v-if="type === 'add' || type === 'distribute'">
        <el-form :model="queryParams" ref="queryRef" :inline="true" label-width="88px" @submit.prevent>
          <el-form-item label="搜索" prop="keyword">
            <el-input
              v-model="queryParams.keyword"
              clearable
              style="width: 250px"
              placeholder="请输入搜索内容"
            />
          </el-form-item>
          <el-form-item label="性别" prop="sex">
            <el-select v-model="queryParams.sex" placeholder="请选择" clearable style="width: 180px">
              <el-option label="男性" :value="1" />
              <el-option label="女性" :value="0" />
            </el-select>
          </el-form-item>
          <el-form-item label="年龄层" prop="ageGroup">
            <el-select
              v-model="queryParams.ageGroup"
              placeholder="请选择"
              multiple
              collapse-tags
              collapse-tags-tooltip
              clearable
              style="width: 180px"
            >
              <el-option
                v-for="item in biz_model_ageGroup"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="擅长品类" prop="specialtyCategory">
            <el-select
              v-model="queryParams.specialtyCategory"
              placeholder="请选择"
              multiple
              collapse-tags
              collapse-tags-tooltip
              clearable
              style="width: 180px"
            >
              <el-option
                v-for="item in modelCategoryList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="模特标签" prop="modelTag">
            <el-select
              v-model="queryParams.modelTag"
              multiple
              collapse-tags
              collapse-tags-tooltip
              clearable
              placeholder="请选择"
              style="width: 180px"
            >
              <el-option v-for="item in modelTagsList" :key="item.id" :label="item.name" :value="item.id" />
            </el-select>
          </el-form-item>
          <el-form-item label="携带单" prop="carry">
            <el-select v-model="queryParams.carry" placeholder="请选择" clearable style="width: 180px">
              <el-option label="有" :value="true" />
              <el-option label="无" :value="false" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button v-btn type="primary" icon="Search" native-type="submit" @click="onQuery">
              搜索
            </el-button>
            <el-button v-btn icon="Refresh" plain @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>
        <SortButton v-model="queryParams.waitsSort" @change="onQuery">待拍数</SortButton>
      </div>
      <div class="list-box" v-loading="loading">
        <div v-if="(type === 'add' || type === 'distribute') && !total" class="empty-box">
          无符合订单要求的模特
        </div>
        <ModelListItem
          v-for="item in modelList"
          :key="item.id"
          ref="ModelListRef"
          :data="item"
          :showMoney="showMoney"
          :type="type"
          :isSelect="modelSelection.includes(item.id)"
          :errorTips="modelSelectError"
          @openPhoto="handleOpenPhoto"
          @select="handleSelect"
        />

        <div class="load-box" v-if="total">
          <LoadMore v-model:status="loadMoreStatus" :loadmore="loadmore" />
        </div>
      </div>

      <ModelLifePhoto ref="ModelLifePhotoRef" />
    </div>

    <template #footer>
      <div class="flex-center">
        <el-button class="round-btn" round @click="close">
          {{ type === 'add' || type === 'distribute' ? '取消' : '确定' }}
        </el-button>
        <el-button
          v-if="type === 'add' || type === 'distribute'"
          class="round-btn"
          round
          type="primary"
          :disabled="!modelSelection.length"
          @click="confirm"
        >
          选择{{ modelSelection.length ? ` (已选择${modelSelection.length}名)` : '' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import SortButton from '@/components/Button/SortButton.vue'
import ModelListItem from '@/views/task/preselection/components/modelListItem'
import ModelLifePhoto from '@/views/model/modelManage/components/modelLifePhoto'
import LoadMore from '@/components/Public/LoadMore'
import { modelCategorySelectRank, modelCategoryTagSelect, addPreselectModelList } from '@/api/model/model'
import {
  addPreselectModel,
  addDistributionModel,
  activePreselectModelList,
  getHistoryMatchList,
  getCanMatchModels,
} from '@/api/order/preselection'

const props = defineProps({
  showMoney: {
    type: Boolean,
    default: true,
  },
})

defineExpose({
  show,
  open,
  close,
})

const emits = defineEmits(['success', 'openSchedule'])

const { proxy } = getCurrentInstance()
const { biz_model_ageGroup } = proxy.useDict('biz_model_ageGroup')

const modelCategoryList = ref([])
const modelTagsList = ref([])

const ModelLifePhotoRef = ref(null)
const dialogVisible = ref(false)
const title = ref('预选模特')
const type = ref('add')
const queryParams = ref({})
const modelList = ref([])
const pageNum = ref(1)
const total = ref(0)
const loading = ref(false)
const loadMoreStatus = ref('loadmore')

const modelLength = ref(0)
const outModelLength = ref(0)

const modelSelection = ref([])
const modelSelectError = ref([])

const onlyHighQuality = ref(false)

const query = ref({})
const matchId = ref('')
// 添加 (预选/分发)
function open(id, t, params) {
  if (t !== 'add' && t !== 'distribute') {
    console.error('preselection type error')
    return
  }
  matchId.value = id
  query.value = params
  type.value = t
  title.value = t === 'distribute' ? '添加分发' : '预选模特'
  resetQuery()
  getModelCategorySelect()
  getModelTagsSelect()
  dialogVisible.value = true
}
// 查看（当前/历史）
function show(id, isHistory) {
  matchId.value = id
  type.value = isHistory ? 'history' : 'view'
  title.value = isHistory ? '预选模特' : '已选模特'
  resetQuery()
  dialogVisible.value = true
}
function close() {
  dialogVisible.value = false
}
function handleClose() {
  modelList.value.length = 0
  pageNum.value = 1
  modelSelection.value.length = 0
  modelSelectError.value.length = 0
  loadMoreStatus.value = 'loadmore'
  modelLength.value = 0
  outModelLength.value = 0
  total.value = 0
  dialogVisible.value = false
}

let lastQueryStr = ''
function onQuery() {
  modelList.value.length = 0
  pageNum.value = 1
  lastQueryStr = Math.random().toString(36).substring(2)
  getList(lastQueryStr)
}
function resetQuery() {
  queryParams.value = {
    keyword: '',
    sex: '',
    carry: '',
    ageGroup: [],
    modelTag: [],
    specialtyCategory: [],
    waitsSort: '',
  }
  onQuery()
}

function getList(str) {
  loading.value = true
  if (type.value === 'add' || type.value === 'distribute') {
    loadMoreStatus.value = 'loading'
    addPreselectModelList({
      ...queryParams.value,
      ...query.value,
      matchId: matchId.value,
      pageNum: pageNum.value,
      pageSize: 10,
      isDistribution: type.value === 'distribute' ? true : false,
    })
      .then(res => {
        if (str !== lastQueryStr) return
        if (res.code == 200) {
          modelList.value.push(...res.data.rows)
          total.value = res.data.total
          if (pageNum.value * 10 >= total.value) {
            loadMoreStatus.value = 'nomore'
          } else {
            loadMoreStatus.value = 'loadmore'
          }
          onlyHighQuality.value = res.msg && res.msg === 'true'
        }
      })
      .catch(() => {
        if (pageNum.value > 1) pageNum.value--
        loadMoreStatus.value = 'loadmore'
      })
      .finally(() => {
        loading.value = false
      })
  } else if (type.value === 'view') {
    activePreselectModelList({ matchId: matchId.value })
      .then(res => {
        if (str !== lastQueryStr) return
        if (res.code == 200) {
          modelList.value = res.data.map(item => {
            let { id, model, ...rest } = item
            return {
              ...model,
              ...rest,
            }
          })
        }
      })
      .finally(() => {
        loading.value = false
      })
  } else if (type.value === 'history') {
    getHistoryMatchList(matchId.value, { isHistory: true })
      .then(res => {
        if (str !== lastQueryStr) return
        if (res.code == 200) {
          res.data?.forEach((item, index) => {
            modelList.value.push({
              id: Math.random(),
              showTitle: `第${item.count}次匹配` + (!index ? `（当前）` : ''),
            })
            item.normalOrderVideoMatchPreselectModelVOS.forEach((m, i) => {
              let { id, model, ...rest } = m
              modelList.value.push({
                ...model,
                ...rest,
              })
              modelLength.value++
            })
            item.outOrderVideoMatchPreselectModelVOS.forEach((m, i) => {
              if (!i) {
                modelList.value.push({
                  id: Math.random(),
                  showTitle: '已淘汰',
                })
              }
              let { id, model, ...rest } = m
              modelList.value.push({
                ...model,
                ...rest,
              })
              outModelLength.value++
            })
          })
        }
      })
      .finally(() => {
        loading.value = false
      })
  }
}
function loadmore() {
  pageNum.value++
  lastQueryStr = Math.random().toString(36).substring(2)
  getList(lastQueryStr)
}

// 模特擅长品类下拉
function getModelCategorySelect() {
  modelCategorySelectRank({ rank: 1, categoryId: 1008 }).then(res => {
    if (res.code == 200) {
      modelCategoryList.value = res.data
    }
  })
}
// 模特标签下拉
function getModelTagsSelect() {
  modelCategoryTagSelect({ categoryId: 1009 }).then(res => {
    if (res.code == 200) {
      modelTagsList.value = res.data
    }
  })
}

function handleOpenPhoto(photos) {
  ModelLifePhotoRef.value?.open(photos)
}
function handleSelect(val, id) {
  if (id) {
    if (val) {
      modelSelection.value.push(id)
    } else {
      modelSelection.value = modelSelection.value.filter(item => item !== id)
    }
  }
}
function confirm() {
  if (!modelSelection.value.length) {
    proxy.$modal.msgWarning('请选择模特')
    return
  }
  let confirmApi
  if (type.value === 'add') {
    confirmApi = addPreselectModel
  } else if (type.value === 'distribute') {
    confirmApi = addDistributionModel
  }

  if (!confirmApi) return
  handleGetCanMatchModels()
  return

  proxy.$modal
    .confirm(
      `确定选择${modelSelection.value.length}名模特吗？`,
      `添加${type.value === 'distribute' ? '分发' : '预选'}提示`
    )
    .then(() => {
      proxy.$modal.loading('提交中')
      confirmApi({
        matchId: matchId.value,
        modelIds: modelSelection.value,
      })
        .then(res => {
          if (res.code == 200) {
            proxy.$modal.closeLoading()
            if (res.data) {
              if (
                Array.isArray(res.data.addModelIds) &&
                Array.isArray(res.data.changeModelIds) &&
                Array.isArray(res.data.intentionModelIds) &&
                Array.isArray(res.data.generalProductLimitModelIds)
              ) {
                modelSelectError.value = [
                  ...res.data.addModelIds.map(item => ({ id: item, type: 1 })),
                  ...res.data.intentionModelIds.map(item => ({ id: item, type: 2 })),
                  ...res.data.changeModelIds.map(item => ({ id: item, type: 3 })),
                  ...res.data.generalProductLimitModelIds.map(item => ({ id: item, type: 4 })),
                ]
                if (modelSelectError.value.length) {
                  let ids = modelSelectError.value.map(item => item.id)
                  modelSelection.value = modelSelection.value.filter(item => !ids.includes(item))
                  proxy.$modal.msgError(res.msg || '列表中存在不满足接单的模特，请检查重试')
                  return
                }
              }
            }
            proxy.$modal.msgSuccess('添加成功')
            // emits('success')
            close()
            handleGetCanMatchModels()
          }
        })
        .catch(() => {
          proxy.$modal.closeLoading()
        })
    })
}

async function handleGetCanMatchModels() {
  proxy.$modal.loading('获取中')
  getCanMatchModels({
    modelIds: modelSelection.value.join(','),
  })
    .then(res => {
      if (res.data?.length) {
        emits('openSchedule', res.data)
      } else {
        emits('success')
      }
    })
    .catch(() => {
      emits('success')
    })
    .finally(() => {
      proxy.$modal.closeLoading()
    })
}
</script>

<style scoped lang="scss">
.content-box {
  font-weight: 500;

  .list-box {
    padding-right: 10px;
    height: 550px;
    overflow-y: auto;

    .empty-box {
      margin: 20% auto;
      width: fit-content;
      height: 20px;
      font-size: 16px;
      color: #adadad;
    }
  }
}
.round-btn {
  padding: 16px 40px;
}
</style>
