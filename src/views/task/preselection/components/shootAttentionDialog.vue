<template>
  <el-dialog
    v-model="dialogVisible"
    title="拍摄注意事项"
    width="500"
    align-center
    append-to-body
    :show-close="true"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="handleClose"
    style="font-weight: 600"
  >
    <div class="content-box">
      <div style="margin-bottom: 20px">
        请用英文填写需要模特注意的拍摄注意事项，填写内容将会同步在模特端的产品详情中供模特查看
      </div>
      <el-form-item label="注意内容" v-loading="loading" ref="formRef" style="position: relative">
        <el-input
          v-model="content"
          type="textarea"
          clearable
          :rows="6"
          placeholder="请用英文填写需要模特注意的拍摄注意事项，填写内容将会同步在模特端的产品详情中供模特查看"
          @input="handleChange"
        />
        <div class="textarea-limit">{{ content.length || 0 }}/3000</div>
      </el-form-item>
      <el-form-item label="上传图片" v-loading="loading">
        <PasteUpload
          v-model="fileList"
          v-if="fileList.length < 5"
          style="width: 100%"
          :limit="5"
          :alwaysShow="true"
          :bucketType="'order'"
          :isClear="true"
          :size="5"
        />
        <div v-if="fileList.length < 5" style="width: 100%; margin: 10px 0;line-height: 1;">
          请上传大小不超过
          <span style="color: #d9001b">5M</span>
          ，格式为
          <span style="color: #d9001b">png/jpg/jpeg</span>
          的图片,最多支持上传5张
        </div>
        <ViewerImageList urlName="picUrl" :data="fileList" is-preview-all @delete="handleFileDelete" />
      </el-form-item>
    </div>
    <template #footer>
      <div class="flex-center">
        <el-button v-btn @click="close">取消</el-button>
        <el-button v-btn type="primary" :loading="disabled" @click="handleSubmit">保存</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import PasteUpload from '@/components/FileUpload/pasteUpload.vue'
import ViewerImageList from '@/components/ImagePreview/viewerImageList.vue'
import { getPreselectModelShootAttention, setPreselectModelShootAttention } from '@/api/order/preselection'
import { chinese_reg } from '@/utils/RegExp'
import { unescapeHtmlMethod } from '@/utils/index.js'

defineExpose({
  open,
  close,
})

const { proxy } = getCurrentInstance()

const emits = defineEmits(['success'])

const dialogVisible = ref(false)
const content = ref('')
const disabled = ref(false)
const loading = ref(false)
const preselectModelId = ref('')
const formRef = ref()
const fileList = ref([])

function open(row) {
  preselectModelId.value = row.pid
  getCount()
  dialogVisible.value = true
}
function close() {
  dialogVisible.value = false
}
function handleClose() {
  disabled.value = false
  loading.value = false
  content.value = ''
  preselectModelId.value = ''
}

function handleChange(val) {
  if (val) {
    if (chinese_reg.test(val)) {
      formRef.value.validateMessage = '请输入英文'
      formRef.value.validateState = 'error'
      return
    }
    if (val.length > 3000) {
      formRef.value.validateMessage = '最多不能超过3000个字符'
      formRef.value.validateState = 'error'
      return
    }
  }
  formRef.value.validateState = 'success'
}

function handleFileDelete(index) {
  fileList.value.splice(index, 1)
}

function getCount() {
  loading.value = true
  getPreselectModelShootAttention({ preselectModelId: preselectModelId.value })
    .then(res => {
      if (res.data) {
        content.value = unescapeHtmlMethod(res.data.shootAttention)
        fileList.value = res.data.shootAttentionObjectKey ? res.data.shootAttentionObjectKey.map(item => ({
          picUrl: item
        })) : []
      }
    })
    .finally(() => {
      loading.value = false
    })
}

function handleSubmit() {
  if (formRef.value.validateState === 'error' || disabled.value || !preselectModelId.value) return
  disabled.value = true
  setPreselectModelShootAttention({
    preselectModelId: preselectModelId.value,
    shootAttention: content.value?.trim() || null,
    shootAttentionObjectKey: fileList.value.length > 0 ? fileList.value.map(item => item.picUrl) : null,
  }).then(res => {
    proxy.$modal.msgSuccess('保存成功')
    emits('success')
    close()
  })
  .finally(() => {
    disabled.value = false
  })
}
</script>

<style scoped lang="scss">
.content-box {
  font-weight: 500;

  .textarea-limit {
    position: absolute;
    bottom: 5px;
    right: 10px;
    background-color: #fff;
    line-height: 1;
  }
}
</style>
