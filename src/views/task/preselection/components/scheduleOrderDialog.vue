<template>
  <el-dialog
    v-model="dialogVisible"
    :title="title"
    width="1200"
    align-center
    append-to-body
    :show-close="false"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="handleClose"
  >
    <div class="content-box">
      <div class="flex-start model-list-box" :class="{ 'cur-box': modelData.length > 1 }">
        <div
          class="flex-start gap-10 model-box"
          v-for="(item, i) in modelData"
          :key="item.id"
          :class="{ cur: item.id === modelInfo.id }"
        >
          <el-avatar
            class="model-avatar"
            icon="UserFilled"
            :size="70"
            :src="$picUrl + item.modelPic + '!1x1compress'"
            style="cursor: pointer;"
            @click="handleChangeModel(i)"
          />
          <div class="model-info" v-if="item.id === modelInfo.id">
            <div class="model-name">
              <span>{{ item.name }}</span>
              <span style="color: #999; font-size: 12px">{{ item.account }}</span>
            </div>
            <div class="flex-start">
              <biz-model-type-new :value="item.modelType" />
              <model-score
                v-if="item.modelCooperationScore || item.modelCooperationScore === 0"
                :score="item.modelCooperationScore"
                style="line-height: normal"
              />
            </div>
            <div class="flex-start gap-5">
              <el-text v-if="item.sex == 1">
                <el-icon color="#777777"><Male /></el-icon>
                男
              </el-text>
              <el-text v-else>
                <el-icon color="#777777"><Female /></el-icon>
                女
              </el-text>
              <biz-model-ageGroup :value="item.ageGroup" tag="text">
                <template v-slot="{ dict }">
                  <el-text>
                    <el-icon color="#3b99fc"><User /></el-icon>
                    {{ dict.label }}
                  </el-text>
                </template>
              </biz-model-ageGroup>
              <biz-nation :value="item.nation" tag="text">
                <template v-slot="{ dict }">
                  <el-text>
                    <el-icon color="#3b99fc"><LocationInformation /></el-icon>
                    {{ dict.label }}
                  </el-text>
                </template>
              </biz-nation>
            </div>
            <div>模特佣金：{{ item.commission }}{{ handleCommissionUnit(item.commissionUnit) }}</div>
          </div>
        </div>
      </div>

      <el-table
        ref="tableRef"
        :header-cell-style="{
          'background-color': '#e8e8ea',
        }"
        :height="600"
        :data="tableData"
        style="width: 100%"
        border
        row-key="key"
        :row-class-name="tableRowClassName"
      >
        <!-- 产品图 -->
        <TableColumns compType="productPic" prop="productPic" label="产品图" width="130" />
        <!-- 产品信息 -->
        <TableColumns
          compType="productInfo"
          prop="productInfo"
          label="产品信息"
          minWidth="280"
          align="left"
        />
        <el-table-column prop="picCount" label="照片数量" align="center" width="80">
          <template v-slot="{ row }">
            <div>{{ handleSelectiveAssembly(row.picCount) }}</div>
          </template>
        </el-table-column>
        <el-table-column prop="intentionModel" label="意向模特" align="center" width="80">
          <template v-slot="{ row }">
            <div>{{ row.intentionModel?.name || '-' }}</div>
          </template>
        </el-table-column>
        <el-table-column prop="shootRequired" label="拍摄建议" align="center" width="80">
          <template v-slot="{ row }">
            <el-button
              v-btn
              v-if="row.shootRequired?.length && checkPermi(['my:preselection:shootRequired'])"
              link
              type="primary"
              @click="handleAction('拍摄建议', row)"
            >
              查看
            </el-button>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column prop="sellingPointProduct" label="产品卖点" align="center" width="80">
          <template v-slot="{ row }">
            <el-button
              v-btn
              v-if="row.sellingPointProduct?.length && checkPermi(['my:preselection:shootingSuggestion'])"
              link
              type="primary"
              @click="handleAction('产品卖点', row)"
            >
              查看
            </el-button>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <!-- 匹配模特注意事项 -->
        <TableColumns
          compType="cautions"
          prop="cautions"
          label="匹配模特注意事项"
          width="180"
          @action="handleAction"
        />
        <el-table-column prop="preselectModelCount" label="参与预选模特" align="center" width="110">
          <template v-slot="{ row }">
            <div>{{ row.preselectModelCount || 0 }}个</div>
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="操作" :align="'center'" width="100">
          <template v-slot="{ row }">
            <div class="select-btn" @click="handleSelect(row)">
              {{ orderSelection.includes(row.videoId) ? '取消' : '选择' }}
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <template #footer>
      <div class="flex-center">
        <el-button class="round-btn" round @click="close">取消</el-button>
        <el-button
          v-if="type === 1"
          class="round-btn"
          round
          type="primary"
          :disabled="!orderSelection.length"
          @click="confirmAdd"
        >
          添加预选{{ orderSelection.length ? ` (已选择${orderSelection.length}单)` : '' }}
        </el-button>
        <template v-if="type === 2">
          <el-button
            v-if="modelData.length > modelDataIndex + 1"
            class="round-btn"
            round
            type="primary"
            :disabled="disabledConfirmBtn"
            @click="handleChangeModel(modelDataIndex + 1)"
          >
            下一个{{ orderSelection.length ? ` (已选择${orderSelection.length}单)` : '' }}
          </el-button>
          <el-button
            v-else
            class="round-btn"
            round
            type="primary"
            :disabled="!cacheOrderNum || disabledConfirmBtn"
            @click="confirmAdd"
          >
            增加预选{{ cacheOrderNum ? ` (已选择${cacheOrderNum}单)` : '' }}
          </el-button>
        </template>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import TableColumns from '@/components/TableColumns/index.vue'
import { bizCommissionUnit } from '@/utils/dict'
import { checkPermi } from '@/utils/permission'
import {
  getMyPreselectFailRecommendList,
  addPreselectFailRecommend,
  getMyPreselectFailMatchList,
  addMatchRecommend,
} from '@/api/order/preselection.js'
import { handleSelectiveAssembly } from '@/views/task/preselection/index.js'

const emits = defineEmits(['success', 'action'])

const { proxy } = getCurrentInstance()

defineExpose({
  open,
  close,
})

const dialogVisible = ref(false)
// 1: 排单 2: 凑单
const type = ref(1)
const modelInfo = ref({})
const modelData = ref([])
const orderSelection = ref([])
const modelDataIndex = ref(0)
const orderSelectionCache = ref([])
const disabledConfirmBtn = ref(false)

const cacheOrderNum = computed(() => {
  let num = 0
  orderSelectionCache.value.forEach(item => {
    num += item.length
  })
  return num
})

const title = ref('')

const tableData = ref([])
const tableLoading = ref(false)

function open(model, t) {
  dialogVisible.value = true
  type.value = t || 1
  title.value = type.value === 1 ? '排单推荐' : '凑单推荐'
  if (Array.isArray(model)) {
    modelData.value = model
    modelInfo.value = model[0]
  } else {
    modelData.value = [model]
    modelInfo.value = model
  }
  getList()
}
function close() {
  dialogVisible.value = false
}
function handleClose() {
  type.value = 1
  modelInfo.value = {}
  orderSelection.value = []
  modelDataIndex.value = 0
  orderSelectionCache.value = []
}

function handleSelect(row) {
  if (orderSelection.value.includes(row.videoId)) {
    orderSelection.value = orderSelection.value.filter(item => item !== row.videoId)
  } else {
    orderSelection.value.push(row.videoId)
  }
  row.key = Math.random().toString(36).substring(2)
}

function tableRowClassName({ row, rowIndex }) {
  if (orderSelection.value.includes(row.videoId)) {
    return 'primary-row'
  }
  return ''
}

function handleChangeModel(index) {
  if (modelDataIndex.value === index) return
  orderSelectionCache.value[modelDataIndex.value] = orderSelection.value.map(item => item)
  if (orderSelectionCache.value[index]) {
    orderSelection.value = orderSelectionCache.value[index]
  } else {
    orderSelection.value = []
    orderSelectionCache.value[index] = []
  }
  modelDataIndex.value = index
  if (index < modelData.value.length) {
    modelInfo.value = modelData.value[index]
    getList()
  }
}

function getList() {
  tableLoading.value = true
  if (type.value === 1) {
    getMyPreselectFailRecommendList({
      modelId: modelInfo.value.id,
      videoId: modelInfo.value.videoId,
    })
      .then(res => {
        tableData.value = res.data
          ? res.data.map(item => ({
              ...item,
              key: Math.random().toString(36).substring(2),
            }))
          : []
      })
      .finally(() => {
        tableLoading.value = false
      })
  } else if (type.value === 2) {
    getMyPreselectFailMatchList({
      modelId: modelInfo.value.id,
    })
      .then(res => {
        tableData.value = res.data
          ? res.data.map(item => ({
              ...item,
              key: Math.random().toString(36).substring(2),
            }))
          : []
      })
      .catch(() => {
        tableData.value = []
      })
      .finally(() => {
        tableLoading.value = false
      })
  }
}

function handleAction(btn, row) {
  emits('action', btn, row)
}

function handleCommissionUnit(val) {
  let b = bizCommissionUnit.find(item => item.value == val)
  return b ? b.label : ''
}

function confirmAdd() {
  if (disabledConfirmBtn.value) return
  disabledConfirmBtn.value = true
  proxy.$modal.loading('正在提交')
  if (type.value === 1) {
    addPreselectFailRecommend({
      addRecommendVideoIds: orderSelection.value,
      modelId: modelInfo.value.id,
      videoId: modelInfo.value.videoId,
    })
      .then(res => {
        proxy.$modal.msgSuccess('添加成功')
        emits('success')
        close()
      })
      .finally(() => {
        disabledConfirmBtn.value = false
        proxy.$modal.closeLoading()
      })
  } else if (type.value === 2) {
    let data = []
    orderSelectionCache.value.forEach((item, index) => {
      if (item && item.length) {
        data.push({
          addRecommendVideoIds: item,
          modelId: modelData.value[index].id,
        })
      }
    })
    if (orderSelection.value.length) {
      data.push({
        addRecommendVideoIds: orderSelection.value,
        modelId: modelData.value[modelDataIndex.value].id,
      })
    }
    addMatchRecommend(data)
      .then(res => {
        proxy.$modal.msgSuccess('添加成功')
        emits('success')
        close()
      })
      .finally(() => {
        disabledConfirmBtn.value = false
        proxy.$modal.closeLoading()
      })
  }
}
</script>

<style scoped lang="scss">
@import '@/components/TableColumns/table.scss';

.content-box {
  .model-list-box {
    padding-bottom: 5px;
    margin-bottom: 8px;
    gap: 15px;
    overflow-x: auto;

    &.cur-box {
      .model-box {
        position: relative;
        flex-shrink: 0;

        & + .cur::before {
          content: '';
          display: block;
          width: 30px;
          height: 1px;
          background-color: #000;
          position: absolute;
          top: 50%;
          left: -45px;
        }
      }
      .cur {
        margin: 0 50px;

        & + .model-box::before {
          content: '';
          display: block;
          width: 30px;
          height: 1px;
          background-color: #00000080;
          position: absolute;
          top: 50%;
          left: -45px;
        }
      }
    }
  }
  .model-box {
    .model-info {
      & > div {
        margin-bottom: 3px;
      }

      .model-name {
        :first-child {
          margin-right: 5px;
          font-weight: 700;
          color: #333;
        }
      }
    }
  }
}
.select-btn {
  margin: 0 auto;
  width: 50px;
  height: 50px;
  line-height: 50px;
  text-align: center;
  border-radius: 50%;
  border: 1px solid #60626680;
  cursor: pointer;

  &:hover {
    border-color: var(--el-color-primary);
    color: var(--el-color-primary);
  }
}
.round-btn {
  min-width: 110px;
}
</style>
