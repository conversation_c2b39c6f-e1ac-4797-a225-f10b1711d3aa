import { picCountOptions } from '@/views/order/list/data.js'
import { ElMessageBox, ElMessage, ElLoading } from 'element-plus'
import ViewerImageList from '@/components/ImagePreview/viewerImageList'
import { h } from 'vue'
import { addPreselectModel } from '@/api/order/preselection'

/**
 * 匹配次数
 */
export const frequencyOptions = [
  { value: 1, label: '1' },
  { value: 2, label: '2' },
  { value: 3, label: '3' },
  { value: 4, label: '4' },
  { value: 5, label: '5' },
  { value: 6, label: '5次以上' },
]
/**
 * 预选人数
 */
export const peoplesOptions = [
  { value: 1, label: '0人' },
  { value: 2, label: '1-3人' },
  { value: 3, label: '4-9人' },
  { value: 4, label: '10人以上' },
]
/**
 * 模特意向
 */
export const modelIntentionOptions = [
  { value: 1, label: '待MT确认' },
  { value: 2, label: 'MT想要' },
  { value: 3, label: 'MT不想要' },
  { value: 4, label: '未确认' },
  { value: 5, label: '超时未选择' },
]
/**
 * 分发结果
 */
export const distributionResultOptions = [
  { value: 1, label: '待处理' },
  { value: 2, label: 'MT想要' },
  { value: 3, label: 'MT不想要' },
  { value: 4, label: '取消分发' },
  { value: 5, label: '订单暂停匹配' },
]
/**
 * 分发结果原因
 */
export const distributionResultCauseOptions = [
  { value: 1, label: '客服取消' },
  { value: 2, label: '订单回退' },
  { value: 3, label: '订单暂停匹配' },
  { value: 4, label: '模特行程中' },
  { value: 5, label: '模特暂停合作' },
  { value: 6, label: '模特取消合作' },
  { value: 7, label: '未确认' },
  { value: 8, label: '交易关闭' },
]
/**
 * 沟通状态
 */
export const communicationOptions = [
  { value: 0, label: '未沟通' },
  { value: 1, label: '沟通中' },
  { value: 2, label: '选定' },
  { value: 3, label: '淘汰' },
]
/**
 * 淘汰原因
 */
export const oustTypeOptions = [
  { value: 1, label: '商家驳回' },
  { value: 2, label: '客服淘汰' },
  { value: 3, label: 'MT不想要' },
  { value: 4, label: '未被选中' },
  { value: 5, label: '超时未选择意向' },
  { value: 6, label: '订单回退' },
  { value: 7, label: '合作状态变更' },
  { value: 8, label: '暂停匹配' },
  { value: 9, label: '交易关闭' },
]
/**
 * 时间范围
 */
export const timeOptions = [
  { value: 1, label: '24小时内' },
  { value: 2, label: '1-2天' },
  { value: 3, label: '2-3天' },
  { value: 4, label: '3天以上' },
]
/**
 * 来源
 */
export const sourceOptions = [
  { value: 1, label: '商家意向' },
  { value: 2, label: '模特自选' },
  { value: 3, label: '客服添加' },
  { value: 4, label: '客服分发' },
  { value: 5, label: '排单推荐' },
  { value: 6, label: '凑单推荐' },
]
/**
 * 淘汰模特原因
 */
export const oustTypeModelOptions = [
  { value: 1, label: '模特表明不想要' },
  { value: 2, label: '模特略过不回复' },
  { value: 3, label: '无适合场地安装' },
  { value: 4, label: '无适配道具协助' },
  { value: 5, label: '拍摄订单过多（已无满足条件的模特）' },
  { value: 6, label: '拍摄要求太复杂' },
  { value: 7, label: '对模特要求过高' },
  { value: 8, label: '有性别要求' },
  { value: 9, label: '有场景（但不愿意/无法安装）' },
  { value: 10, label: '其他' },
]

/**
 * 处理产品信息复制
 * @param {*} row
 * @returns
 */
export function handleCopy(row) {
  let str = ''
  let shootRequired = ''
  if (row.shootRequired?.length) {
    shootRequired = row.shootRequired
      .map((item, index) => {
        return `   (${index + 1})${item.content}`
      })
      .join('\n')
  }
  str = `${row.videoCode || ''}\n${row.productChinese || ''}\n${row.productEnglish}\n`
  str += `${row.productLink || ''}\n`
  let strArr = [
    `Video Style：${row.videoStyle == 2 ? 'Digital Content' : row.videoStyle == 1 ? 'Tiktok Style' : row.videoStyle == 0 ? 'Amazon Style' : ''}`,
    `Recording Orientation：${row.videoFormat == 1 ? 'Horizontal recording' : 'Vertical shooting'}`,
    row.videoDuration ? `Video Duration：${row.videoDuration}s` : '',
    `Take photos：${row.surplusPicCount ? row.surplusPicCount + 'photos' : ''}`,
    row.referenceVideoLink ? `Reference video：${row.referenceVideoLink}` : '',
    shootRequired ? `Videoing requirement：\n${shootRequired}` : '',
  ]
  // if (row.picCount != 1 && row.picCount != 2) {
  //   strArr.splice(3, 1)
  // }
  if (!row.surplusPicCount || row.surplusPicCount == 0) {
    strArr.splice(3, 1)
  }
  if (row.platform == 3 || row.videoStyle == 2) {
    strArr.shift(1)
  }
  str += strArr
    .filter(Boolean)
    .map((item, index) => `${index + 1}.${item}`)
    .join('\n')
  return str
}

/**
 * 处理照片数量
 * @param {*} val
 * @returns
 */
export function handleSelectiveAssembly(val) {
  let str = picCountOptions.find(item => item.value == val)
  return str ? str.label.substring(0, 2) : '-'
}

/**
 * 按钮禁用
 * @param {*} data
 * @returns
 */
export function isRefund(data) {
  return (
    data != null &&
    data.refundType == 2 &&
    (data.refundStatus == 0 || data.refundStatus == 1 || data.refundStatus == 4)
  )
}
/**
 * 订单状态天数
 * @param {*} time
 * @returns
 */
export function handleOrderStatusTime(time) {
  if (!time) return ''
  // let t = time.split(' ')[0]
  let distance = new Date().getTime() - new Date(time).getTime()
  let day = Math.floor(distance / (1000 * 60 * 60 * 24))
  let hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
  if (hours < 0) hours = 0
  return '已' + (day > 0 ? `${day}天` : '') + hours + '小时'
}

/**
 * 查看匹配模特注意事项
 * @param {Array} arr
 * @returns
 */
export function handleViewCautions(data, type) {
  if (data) {
    let contentDom = []
    data.cautions.forEach((item, i) => {
      contentDom.push(h('span', `${item.content}`))
      contentDom.push(h('br'))
    })
    let dom = h('div', [
      h(
        'div',
        {
          style: {
            'max-height': '500px',
            'overflow-y': 'auto',
            'margin-bottom': '10px',
            'font-weight': '500',
            'line-break': 'anywhere',
            'white-space': 'pre-wrap',
          },
        },
        contentDom
      ),
      h(ViewerImageList, {
        data: data.cautionsPics || [],
        customized: true,
        isPreviewAll: true,
        showDownloadBtn: false,
        showDeleteBtn: false,
      }),
    ])

    ElMessageBox.alert(dom, '模特要求', {
      customStyle: {
        '--el-messagebox-width': '680px',
        'font-weight': '600',
      },
      dangerouslyUseHTMLString: true,
      showConfirmButton: false,
      showCancelButton: false,
      closeOnClickModal: true,
    })
  }
}
export function handleViewSpecificationRequire(data) {
  if (data) {
    let contentDom = []
    contentDom.push(h('span', `${data}`))
    // data.cautions.forEach((item, i) => {
    //   contentDom.push(h('span', `${item.content}`))
    //   contentDom.push(h('br'))
    // })
    let dom = h('div', [
      h(
        'div',
        {
          style: {
            'max-height': '500px',
            'overflow-y': 'auto',
            'margin-bottom': '10px',
            'font-weight': '500',
            'line-break': 'anywhere',
            'white-space': 'pre-wrap',
          },
        },
        contentDom
      ),
      h(ViewerImageList, {
        data: data.cautionsPics || [],
        customized: true,
        isPreviewAll: true,
        showDownloadBtn: false,
        showDeleteBtn: false,
      }),
    ])

    ElMessageBox.alert(dom, '商品规格要求', {
      customStyle: {
        '--el-messagebox-width': '680px',
        'font-weight': '600',
      },
      dangerouslyUseHTMLString: true,
      showConfirmButton: false,
      showCancelButton: false,
      closeOnClickModal: true,
    })
  }
}

export function handleViewRequire(data) {
  if (data) {
    let contentDom = []
    let contentDomV1 = []
    if (data.orderSpecificationRequire && data.orderSpecificationRequire.length) {
      contentDom.push(h('div', `${data.orderSpecificationRequire}`))
    }
    let contentDomV3 = data.particularEmphasis
    if (
      data.orderVideoCautionsVO &&
      data.orderVideoCautionsVO.cautions.length &&
      data.orderVideoCautionsVO.cautions.length > 0
    ) {
      data.orderVideoCautionsVO.cautions.forEach((item, i) => {
        contentDomV1.push(h('span', `${item.content}`))
        contentDomV1.push(h('br'))
      })
    }
    let dom = h('div', [
      contentDomV1 &&
      contentDomV1.length > 0 &&
      h(
        'div',
        {
          style: {
            'margin-bottom': '10px',
            'font-weight': '500',
          },
        },
        '模特要求'
      ),
      contentDomV1 &&
      contentDomV1.length > 0 &&
      h(
        'div',
        {
          style: {
            'max-height': '100px',
            'overflow-y': 'auto',
            'margin-bottom': '10px',
            'font-weight': '500',
            'line-break': 'anywhere',
            'white-space': 'pre-wrap',
            background: '#f2f2f2',
            padding: '10px 20px',
          },
        },
        contentDomV1
      ),
      contentDom &&
      contentDom.length > 0 &&
      h(
        'div',
        {
          style: {
            'margin-bottom': '10px',
            'font-weight': '500',
          },
        },
        '商品规格要求'
      ),
      contentDom &&
      contentDom.length > 0 &&
      h(
        'div',
        {
          style: {
            'max-height': '100px',
            'overflow-y': 'auto',
            'margin-bottom': '10px',
            'font-weight': '500',
            'line-break': 'anywhere',
            'white-space': 'pre-wrap',
            background: '#f2f2f2',
            padding: '10px 20px',
          },
        },
        contentDom
      ),
      ((contentDomV3 && contentDomV3.length > 0) ||
        (data.particularEmphasisPic &&
          data.particularEmphasisPic.length > 0)) &&
      h(
        'div',
        {
          style: {
            'margin-bottom': '10px',
            'font-weight': '500',
          },
        },
        '特别强调'
      ),
      ((contentDomV3 && contentDomV3.length > 0) ||
        (data.particularEmphasisPic &&
          data.particularEmphasisPic.length > 0)) &&
      h(
        'div',
        {
          style: {
            'max-height': '400px',
            'overflow-y': 'auto',
            'margin-bottom': '10px',
            'font-weight': '500',
            'line-break': 'anywhere',
            'white-space': 'pre-wrap',
            background: '#f2f2f2',
            padding: '10px 20px',
          },
        },
        contentDomV3,
        h(ViewerImageList, {
          data: data.particularEmphasisPic || [],
          customized: true,
          isPreviewAll: true,
          showDownloadBtn: false,
          showDeleteBtn: false,
        })
      ),
    ])

    ElMessageBox.alert(dom, '匹配模特注意事项', {
      customStyle: {
        '--el-messagebox-width': '680px',
        'font-weight': '600',
      },
      customClass: 'custom-message-boxV1',
      dangerouslyUseHTMLString: true,
      showConfirmButton: false,
      showCancelButton: false,
      closeOnClickModal: true,
    })
  }
}

/**
 * 查看匹配模特注意事项
 * @param {String} data
 * @returns
 */
export function handleViewEmphasis(data) {
  if (data) {
    let contentDom = data.particularEmphasis
    let dom = h('div', [
      h(
        'div',
        {
          style: {
            'max-height': '500px',
            'overflow-y': 'auto',
            'margin-bottom': '10px',
            'font-weight': '500',
            'line-break': 'anywhere',
            'white-space': 'pre-wrap',
          },
        },
        contentDom
      ),
      h(ViewerImageList, {
        data: data.particularEmphasisPic || [],
        customized: true,
        isPreviewAll: true,
        showDownloadBtn: false,
        showDeleteBtn: false,
      }),
    ])

    ElMessageBox.alert(dom, '特别强调', {
      customStyle: {
        '--el-messagebox-width': '680px',
        'font-weight': '600',
      },
      dangerouslyUseHTMLString: true,
      showConfirmButton: false,
      showCancelButton: false,
      closeOnClickModal: true,
    })
  }
}

/**
 * 判断预选模特状态
 * @param {*} data
 * @returns
 */
// export function handleCheckModelStatus(data) {
//   return new Promise((resolve, reject) => {
//     const el_loading = ElLoading.service({
//       lock: true,
//       text: content,
//       background: "rgba(0, 0, 0, 0.7)",
//     })
//     checkModelStatus(data).then((res) => {
//       handleSelectedModelStatus(res.data).then(() => {
//         resolve(true)
//       }).catch(msg => {
//         reject(msg)
//       })
//     }).catch(err => {
//       reject(err)
//     }).finally(() => {
//       el_loading.close()
//     })
//   })
// }

/**
 * 判断预选模特状态
 * @param {*} data
 * @returns
 */
export function handleSelectedModelStatus(data) {
  return new Promise((resolve, reject) => {
    if (
      (typeof data === 'string' &&
        (data?.indexOf('暂停') > -1 ||
          data?.indexOf('取消') > -1 ||
          data?.indexOf('拉黑') > -1 ||
          data?.indexOf('行程') > -1 ||
          data?.indexOf('逾期') > -1)) ||
      data?.indexOf('下架') > -1
    ) {
      let msg = ''
      if (data.indexOf('暂停') > -1) {
        msg = '该模特已暂停合作，无法选择其为拍摄模特'
      } else if (data.indexOf('取消') > -1) {
        msg = '该模特已取消合作，无法选择其为拍摄模特'
      } else if (data.indexOf('拉黑') > -1) {
        msg = '该模特被商家拉黑，无法选择其为拍摄模特'
      } else if (data.indexOf('行程') > -1) {
        msg = '该模特行程中，无法选择其为拍摄模特'
      } else if (data.indexOf('逾期') > -1) {
        msg = '模特订单拍摄超时，无法接单'
      } else if (data.indexOf('下架') > -1) {
        msg = '该模特已下架，无法选择其为拍摄模特'
      }
      reject(msg)
      return
    }
    resolve(true)
  })
}

/**
 * 模特淘汰原因
 * @param {*} val 
 * @param {*} type 
 * @param {*} isContent 
 * @returns 
 */
export function handleOustRemark(remark, type, isContent) {
  let t = oustTypeOptions.find(item => item.value == type)
  if (isContent) {
    if (t && t.label == '客服淘汰') {
      return remark ? remark.split('-')[1] : ''
    }
    return remark ? remark : ''
  }
  let r = remark ? remark.split('-')[0] : ''
  if (t && t.label == '客服淘汰') {
    return r ? `${t.label}-${r}` : ''
  }
  return t ? t.label : ''
}

/**
 * 恢复预选
 * @param {*} m 
 */
export function handleRestorePreselection(m, successFun) {
  const el_loading = ElLoading.service({
    lock: true,
    text: '恢复中',
    background: "rgba(0, 0, 0, 0.7)",
  })
  addPreselectModel({
    matchId: m.matchId,
    modelIds: m.modelIds,
  })
    .then(res => {
      if (res.data.generalProductLimitModelIds?.length) {
        ElMessage.error('模特评分不满足订单条件')
        return
      }
      ElMessage.success('操作成功')
      if (successFun) successFun()
    })
    .finally(() => {
      el_loading.close()
    })
}
