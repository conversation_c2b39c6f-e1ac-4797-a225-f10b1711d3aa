import { createWebHistory, createRouter } from 'vue-router'
/* Layout */
import Layout from '@/layout'

/**
 * Note: 路由配置项
 *
 * hidden: true                     // 当设置 true 的时候该路由不会再侧边栏出现 如401，login等页面，或者如一些编辑页面/edit/1
 * alwaysShow: true                 // 当你一个路由下面的 children 声明的路由大于1个时，自动会变成嵌套的模式--如组件页面
 *                                  // 只有一个时，会将那个子路由当做根路由显示在侧边栏--如引导页面
 *                                  // 若你想不管路由下面的 children 声明的个数都显示你的根路由
 *                                  // 你可以设置 alwaysShow: true，这样它就会忽略之前定义的规则，一直显示根路由
 * redirect: noRedirect             // 当设置 noRedirect 的时候该路由在面包屑导航中不可被点击
 * name:'router-name'               // 设定路由的名字，一定要填写不然使用<keep-alive>时会出现各种问题
 * query: '{"id": 1, "name": "ry"}' // 访问路由的默认传递参数
 * roles: ['admin', 'common']       // 访问路由的角色权限
 * permissions: ['a:a:a', 'b:b:b']  // 访问路由的菜单权限
 * meta : {
    noCache: true                   // 如果设置为true，则不会被 <keep-alive> 缓存(默认 false)
    title: 'title'                  // 设置该路由在侧边栏和面包屑中展示的名字
    icon: 'svg-name'                // 设置该路由的图标，对应路径src/assets/icons/svg
    breadcrumb: false               // 如果设置为false，则不会在breadcrumb面包屑中显示
    activeMenu: '/system/user'      // 当路由设置了该属性，则会高亮相对应的侧边栏。
  }
 */

// 公共路由
export const constantRoutes = [
  {
    path: '/redirect',
    component: Layout,
    hidden: true,
    children: [
      {
        path: '/redirect/:path(.*)',
        component: () => import('@/views/redirect/index.vue'),
      },
    ],
  },
  {
    path: '/login',
    component: () => import('@/views/login'),
    hidden: true,
  },
  {
    path: '/register',
    component: () => import('@/views/register'),
    hidden: true,
  },
  {
    path: '/:pathMatch(.*)*',
    component: () => import('@/views/error/404'),
    hidden: true,
  },
  {
    path: '/401',
    component: () => import('@/views/error/401'),
    hidden: true,
  },
  {
    path: '',
    component: Layout,
    redirect: '/index',
    children: [
      {
        path: '/index',
        hidden: false,
        component: () => import('@/views/index'),
        name: 'Index',
        meta: { title: '首页', icon: 'dashboard', affix: true },
      },
    ],
  },
  {
    path: '/user',
    component: Layout,
    hidden: true,
    redirect: 'noredirect',
    children: [
      {
        path: 'profile',
        component: () => import('@/views/system/user/profile/index'),
        name: 'Profile',
        meta: { title: '个人中心', icon: 'user' },
      },
    ],
  },
]

// 动态路由，基于用户权限动态去加载
export const dynamicRoutes = [
  {
    path: '/system/user-auth',
    component: Layout,
    hidden: true,
    permissions: ['system:user:edit'],
    children: [
      {
        path: 'role/:userId(\\d+)',
        component: () => import('@/views/system/user/authRole'),
        name: 'AuthRole',
        meta: { title: '分配权限', activeMenu: '/system/user' },
      },
    ],
  },
  {
    path: '/system/role-auth',
    component: Layout,
    hidden: true,
    permissions: ['system:role:edit'],
    children: [
      {
        path: 'user/:roleId(\\d+)',
        component: () => import('@/views/system/role/authUser'),
        name: 'AuthUser',
        meta: { title: '分配用户', activeMenu: '/system/role' },
      },
    ],
  },
  {
    path: '/system/dict-data',
    component: Layout,
    hidden: true,
    permissions: ['system:dict:list'],
    children: [
      {
        path: 'index/:dictId(\\d+)',
        component: () => import('@/views/system/dict/data'),
        name: 'Data',
        meta: { title: '字典数据', activeMenu: '/system/dict' },
      },
    ],
  },
  {
    path: '/tool/gen-edit',
    component: Layout,
    hidden: true,
    permissions: ['tool:gen:edit'],
    children: [
      {
        path: 'index/:tableId(\\d+)',
        component: () => import('@/views/tool/gen/editTable'),
        name: 'GenEdit',
        meta: { title: '修改生成配置', activeMenu: '/tool/gen' },
      },
    ],
  },
  {
    path: '/model/handleModel',
    component: Layout,
    hidden: true,
    permissions: ['model:manage:list'],
    children: [
      {
        path: 'add/:modelId(\\d+)?',
        permissions: ['model:manage:add'],
        component: () => import('@/views/model/modelManage/handleModel'),
        name: 'HandleAddModel',
        meta: { title: '新增模特', activeMenu: '/model/model/modelManage' },
      },
      {
        path: 'edit/:modelId(\\d+)',
        permissions: ['model:manage:edit'],
        component: () => import('@/views/model/modelManage/handleModel'),
        name: 'HandleEditModel',
        meta: { title: '编辑模特', activeMenu: '/model/model/modelManage' },
      },
      {
        path: 'read/:readonly(\\d+)/:modelId(\\d+)',
        component: () => import('@/views/model/modelManage/handleModel'),
        name: 'HandleReadModel',
        meta: { title: '模特详情', activeMenu: '/model/model/modelManage' },
      },
    ],
  },
  {
    path: '/model/modelData/details',
    component: Layout,
    hidden: true,
    permissions: ['model:data:list'],
    children: [
      {
        path: ':modelId(\\d+)',
        component: () => import('@/views/model/modelData/details/index'),
        name: 'ModelDetails',
        meta: { title: '模特详情', activeMenu: '/model/modelData' },
      },
    ],
  },
  {
    path: '/order/details',
    component: Layout,
    hidden: true,
    permissions: ['order:manage:details'],
    children: [
      {
        path: ':videoId',
        permissions: ['order:manage:details'],
        component: () => import('@/views/order/details/index'),
        name: 'OrderDetails',
        meta: { title: '订单详情', activeMenu: '/order/list' },
      },
    ],
  },
  {
    path: '/order/details/member',
    component: Layout,
    hidden: true,
    permissions: ['order:vip:details'],
    children: [
      {
        path: ':orderNum', // 会员订单
        permissions: ['order:vip:details'],
        component: () => import('@/views/order/details/member'),
        name: 'OrderMemberDetails',
        meta: { title: '会员订单详情', activeMenu: '/order/member' },
      },
    ],
  },
  {
    path: '/task/info/',
    component: Layout,
    hidden: true,
    permissions: ['task:work:list'],
    children: [
      {
        path: ':taskNum(\\d+)',
        permissions: ['task:work:list'],
        component: () => import('@/views/task/info'),
        name: 'TaskDetails',
        meta: { title: '工单详情', activeMenu: '/task/info' },
      },
    ],
  },
  {
    path: '/merchant/balance/info/',
    component: Layout,
    hidden: true,
    permissions: ['merchant:balance:details'],
    children: [
      {
        path: ':businessId(\\d+)',
        permissions: ['merchant:balance:details'],
        component: () => import('@/views/merchant/balance/info'),
        name: 'MerchantBalanceDetails',
        meta: { title: '商家余额详情', activeMenu: '/merchant/balance/info' },
      },
    ],
  },
  {
    path: '/merchant/returnVisit/',
    component: Layout,
    hidden: true,
    permissions: ['merchant:returnVisit:record'],
    children: [
      {
        path: 'records/:id(\\d+)',
        permissions: ['merchant:returnVisit:record'],
        component: () => import('@/views/merchant/returnVisit/records'),
        name: 'MerchantReturnVisitRecords',
        meta: { title: '回访记录', activeMenu: '/merchant/returnVisit/list' },
      },
    ],
  },
  {
    path: '/case/group-video',
    component: Layout,
    hidden: true,
    permissions: ['case:group:manage'],
    children: [
      {
        path: ':id(\\d+)',
        permissions: ['case:group:manage'],
        component: () => import('@/views/case/group/videoList'),
        name: 'CaseGroupVideo',
        meta: { title: '分组视频管理', activeMenu: '/case/group' },
      },
    ],
  },
  {
    path: '/custom/home-page',
    component: Layout,
    hidden: true,
    permissions: ['custom:page:list'],
    children: [
      {
        path: 'add',
        permissions: ['custom:home-page:add'],
        component: () => import('@/views/custom/home/<USER>'),
        name: 'CustomHomePageAdd',
        meta: { title: '首页配置', activeMenu: '/custom/list' },
      },
      {
        path: 'edit/:pageId(\\d+)',
        permissions: ['custom:home-page:edit'],
        component: () => import('@/views/custom/home/<USER>'),
        name: 'CustomHomePageEdit',
        meta: { title: '首页配置', activeMenu: '/custom/list' },
      },
    ],
  },
  {
    path: '/custom/case-page',
    component: Layout,
    hidden: true,
    permissions: ['custom:page:list'],
    children: [
      {
        path: 'add',
        permissions: ['custom:case-page:add'],
        component: () => import('@/views/custom/case/index'),
        name: 'CustomCasePageAdd',
        meta: { title: '精选案例配置', activeMenu: '/custom/list' },
      },
      {
        path: 'edit/:platform(\\d+)/:pageId(\\d+)',
        permissions: ['custom:case-page:edit'],
        component: () => import('@/views/custom/case/index'),
        name: 'CustomCasePageEdit',
        meta: { title: '精选案例配置', activeMenu: '/custom/list' },
      },
    ],
  },
  {
    path: '/channel/distribution/detail',
    component: Layout,
    hidden: true,
    permissions: ['channel:distribution:detail'],
    children: [
      {
        path: ':id(\\d+)', // 渠道详情
        permissions: ['channel:distribution:detail'],
        component: () => import('@/views/channel/distribution/detail/index'),
        name: 'distributionDetail',
        meta: { title: '分销渠道详情', activeMenu: '/channel/distribution' },
      },
    ],
  },
  {
    path: '/channel/market/detail',
    component: Layout,
    hidden: true,
    permissions: ['channel:market:detail'],
    children: [
      {
        path: ':id(\\d+)', // 渠道详情
        permissions: ['channel:market:detail'],
        component: () => import('@/views/channel/market/detail/index'),
        name: 'marketDetail',
        meta: { title: '市场渠道详情', activeMenu: '/channel/market' },
      },
    ],
  },
  {
    path: '/finance/receivableApprove/detail',
    component: Layout,
    hidden: true,
    permissions: ['finance:receivable:approve'],
    children: [
      {
        path: 'video/:id(\\d+)', // 视频审批详情
        permissions: ['receivable:approve:video:detail'],
        component: () => import('@/views/finance/receivableApprove/details/video'),
        name: 'videoApproveDetail',
        meta: { title: '视频审批详情', activeMenu: '/finance/receivableApprove' },
      },
      {
        path: 'member/:id(\\d+)', // 会员审批详情
        permissions: ['receivable:approve:member:detail'],
        component: () => import('@/views/finance/receivableApprove/details/member'),
        name: 'memberApproveDetail',
        meta: { title: '会员审批详情', activeMenu: '/finance/receivableApprove' },
      },
    ],
  },
  {
    path: '/task/afterSale/detail',
    component: Layout,
    hidden: true,
    permissions: ['task:afterSale:detail'],
    children: [
      {
        path: ':id(\\d+)', // 售后详情
        permissions: ['task:afterSale:detail'],
        component: () => import('@/views/task/afterSale/detail'),
        name: 'afterSaleDetail',
        meta: { title: '售后详情', activeMenu: '/task/index' },
      },
    ],
  },
  {
    path: '/task/workOrder/detail',
    component: Layout,
    hidden: true,
    permissions: ['task:workOrder:detail'],
    children: [
      {
        path: ':id(\\d+)', // 工单详情
        permissions: ['task:workOrder:detail'],
        component: () => import('@/views/task/workOrder/detail'),
        name: 'workOrderDetail',
        meta: { title: '工单详情', activeMenu: '/task/index' },
      },
    ],
  },
  {
    path: '/channel/activity',
    component: Layout,
    hidden: true,
    permissions: ['channel:activity:list'],
    children: [
      {
        path: 'add',
        permissions: ['channel:activity:add'],
        component: () => import('@/views/channel/activity/data.vue'),
        name: 'ChannelActivityAdd',
        meta: { title: '新增渠道活动', activeMenu: '/channel/activity/list' },
      },
      {
        path: 'view/:id(\\d+)',
        permissions: ['channel:activity:view'],
        component: () => import('@/views/channel/activity/data.vue'),
        name: 'ChannelActivityDetail',
        meta: { title: '渠道活动详情', activeMenu: '/channel/activity/list' },
      },
      {
        path: 'edit/:id(\\d+)',
        permissions: ['channel:activity:edit'],
        component: () => import('@/views/channel/activity/data.vue'),
        name: 'ChannelActivityEdit',
        meta: { title: '编辑渠道活动', activeMenu: '/channel/activity/list' },
      },
    ],
  },
  {
    path: '/channel/memberActivity',
    component: Layout,
    hidden: true,
    permissions: ['channel:memberActivity:list'],
    children: [
      {
        path: 'add',
        permissions: ['channel:memberActivity:add'],
        component: () => import('@/views/channel/memberActivity/data.vue'),
        name: 'ChannelMemberActivityAdd',
        meta: { title: '新增会员活动', activeMenu: '/channel/memberActivity/list' },
      },
      {
        path: 'view/:id(\\d+)',
        permissions: ['channel:memberActivity:view'],
        component: () => import('@/views/channel/memberActivity/data.vue'),
        name: 'ChannelMemberActivityDetail',
        meta: { title: '会员活动详情', activeMenu: '/channel/memberActivity/list' },
      },
      {
        path: 'edit/:id(\\d+)',
        permissions: ['channel:memberActivity:edit'],
        component: () => import('@/views/channel/memberActivity/data.vue'),
        name: 'ChannelMemberActivityEdit',
        meta: { title: '编辑会员活动', activeMenu: '/channel/memberActivity/list' },
      },
    ],
  },
  {
    path: '/channel/fission/detail',
    component: Layout,
    hidden: true,
    permissions: ['channel:fission:detail'],
    children: [
      {
        path: ':id(\\d+)', // 渠道详情
        permissions: ['channel:fission:detail'],
        component: () => import('@/views/channel/fission/detail/index'),
        name: 'fissionDetail',
        meta: { title: '裂变拉新详情', activeMenu: '/channel/fission' },
      },
    ],
  },
]

const router = createRouter({
  history: createWebHistory(),
  routes: constantRoutes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  },
})

export default router
