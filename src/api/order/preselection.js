import request from '@/utils/request'

//预选管理-模特匹配-订单池
export function getOrderPoolList(params) {
  return request({
    url: '/order/match/order-pool-list',
    method: 'get',
    params,
  })
}

//预选管理-历史预选模特列表
export function getHistoryMatchList(videoId, params) {
  return request({
    url: `/order/match/history-match-list/${videoId}`,
    method: 'get',
    params,
  })
}

//预选管理-添加预选模特
export function addPreselectModel(data) {
  return request({
    url: `/order/match/add-preselect-model`,
    method: 'post',
    data,
  })
}

//预选管理-添加分发
export function addDistributionModel(data) {
  return request({
    url: `/order/match/add-distribution`,
    method: 'post',
    data,
  })
}

//预选管理-查询当前匹配单活跃的预选模特
export function activePreselectModelList(params) {
  return request({
    url: `/order/match/active-preselect-model-list/${params.matchId}`,
    method: 'get',
    params,
  })
}

//预选管理-模特匹配-我的预选-沟通中
export function getMyPreselectDockingList(params) {
  return request({
    url: `/order/match/my-preselect-docking-list`,
    method: 'get',
    params,
  })
}

//预选管理-模特匹配-我的预选-失败待确认
export function getMyPreselectFailList(params) {
  return request({
    url: `/order/match/my-preselect-fail-list`,
    method: 'get',
    params,
  })
}

//预选管理-模特匹配-我的预选-失败待确认列表-同链接订单
export function getMyPreselectSameProductList(params) {
  return request({
    url: `/order/match/same-product-list`,
    method: 'get',
    params,
  })
}

//预选管理-失败待确认列表-排单推荐列表
export function getMyPreselectFailRecommendList(params) {
  return request({
    url: `/order/match/my-preselect-fail-list-recommend-list`,
    method: 'get',
    params,
  })
}

//预选管理-模特匹配-我的预选-失败待确认列表-添加排单推荐
export function addPreselectFailRecommend(data) {
  return request({
    url: `/order/match/add-recommend`,
    method: 'post',
    data,
  })
}

//预选管理-获取可凑单的模特
export function getCanMatchModels(params) {
  return request({
    url: `/order/match/get-can-match-model`,
    method: 'get',
    params,
  })
}

//预选管理-模特匹配-我的预选-失败待确认列表-凑单推荐列表
export function getMyPreselectFailMatchList(params) {
  return request({
    url: `/order/match/my-preselect-fail-list-match-list`,
    method: 'get',
    params,
  })
}

//预选管理-模特匹配-我的预选-失败待确认列表-添加凑单推荐
export function addMatchRecommend(data) {
  return request({
    url: `/order/match/add-match-recommend`,
    method: 'post',
    data,
  })
}

//预选管理-我的预选-分发中
export function getMyPreselectDistributionList(params) {
  return request({
    url: `/order/match/my-preselect-distribution-list`,
    method: 'get',
    params,
  })
}

//预选管理-模特匹配-我的预选-结束预选
export function getMyPreselectEndMatchList(params) {
  return request({
    url: `/order/match/my-preselect-end-match-list`,
    method: 'get',
    params,
  })
}

//预选管理-更改预选模特状态
export function editPreselectModel(data) {
  return request({
    url: `/order/match/edit-preselect-model`,
    method: 'post',
    data,
  })
}

//预选管理-失败待确认列表-确认淘汰
export function confirmOustPreselectModel(params) {
  return request({
    url: `/order/match/confirm-oust`,
    method: 'put',
    params,
  })
}

//预选管理-批量标记沟通
export function batchMarkCommunication(data) {
  return request({
    url: `/order/match/batch-mark-communication`,
    method: 'post',
    data,
  })
}

//预选管理-设置模特分发结果
export function setModelDistributionResult(data) {
  return request({
    url: `/order/match/set-model-distribution-result`,
    method: 'post',
    data,
  })
}

//预选管理-选定模特
export function selectedModel(data) {
  return request({
    url: `/order/match/selected-model`,
    method: 'post',
    data,
  })
}
//预选管理-修改选定模特
export function editSelectedModel(data) {
  return request({
    url: `/order/match/edit-selected-model`,
    method: 'put',
    data,
  })
}

//预选管理-主携带订单下拉框
export function selectModelMainCarry(params) {
  return request({
    url: `/order/match/selected-model-main-carry-list`,
    method: 'get',
    params,
  })
}

//预选管理-选定模特信息
export function getSelectModelInfo(params) {
  return request({
    url: `/order/match/selected-model-info`,
    method: 'get',
    params,
  })
}

//预选管理-确认提交预选模特
export function submitModel(data) {
  return request({
    url: `/order/match/submit-video`,
    method: 'post',
    data,
  })
}

//预选管理-我的预选-历史分发记录
export function distributionRecord(params) {
  return request({
    url: `/order/match/distribution-history-list`,
    method: 'get',
    params
  })
}

//预选管理-结束预选-预选记录
export function preselectionRecord(matchId) {
  return request({
    url: `/order/match/preselection-record/${matchId}`,
    method: 'get',
  })
}

//暂停匹配单
export function pauseMatch(data) {
  return request({
    url: `/order/match/pause-match`,
    method: 'post',
    data,
  })
}

//继续模特匹配
export function continueMatch(params) {
  return request({
    url: `/order/match/continue-match`,
    method: 'put',
    params,
  })
}

//修改订单信息
export function editMatchVideoOrder(data) {
  return request({
    url: `/order/match/edit-order-video`,
    method: 'post',
    data,
  })
}

//翻译拍摄要求
export function translateShootRequired(data) {
  return request({
    url: `/order/match/translate-shoot-required/${data.matchId}`,
    method: 'post',
    data,
  })
}

///order/match/check-model-an-order-with-the-same-product-link-exists
//检查模特是否已经存在相同产品链接的订单
export function checkModelAnOrder(params) {
  return request({
    url: `/order/match/check-model-an-order-with-the-same-product-link-exists`,
    method: 'get',
    params,
  })
}

// 预选管理-预选模特下拉框
export function myPreselectDockingModelSelect() {
  return request({
    url: `/order/match/my-preselect-docking-model-select`,
    method: 'get',
  })
}

// 预选管理-拍摄模特下拉框
export function myPreselectShootModelSelect() {
  return request({
    url: `/order/match/my-preselect-shoot-model-select`,
    method: 'get',
  })
}

// 预选管理-订单池-英文部客服下拉框
export function poolIssueEnglishSelect() {
  return request({
    url: `/order/match/pool-issue-select`,
    method: 'get',
  })
}

// 预选管理-分发中-英文部客服下拉框
export function distributionEnglishSelect() {
  return request({
    url: `/order/match/my-preselect-distribution-english-select`,
    method: 'get',
  })
}

// 预选管理-分发中-分发模特下拉框
export function distributionModelSelect() {
  return request({
    url: `/order/match/my-preselect-distribution-model-select`,
    method: 'get',
  })
}

// 预选管理-模特匹配-我的预选-失败待确认列表-淘汰模特下拉框
export function myPreselectOustModelSelect() {
  return request({
    url: `/order/match/my-preselect-fail-list-model-select`,
    method: 'get',
  })
}

// 预选管理-查询预选模特拍摄注意事项
export function getPreselectModelShootAttention(params) {
  return request({
    url: `/order/match/preselect-model-shoot-attention`,
    method: 'get',
    params
  })
}

// 预选管理-填写模特拍摄注意事项
export function setPreselectModelShootAttention(data) {
  return request({
    url: `/order/match/preselect-model-shoot-attention`,
    method: 'post',
    data
  })
}
